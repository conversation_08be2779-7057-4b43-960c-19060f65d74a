{"title": "Board", "views": {"board": "Board View", "list": "List View", "calendar": "Calendar View"}, "column": {"color": "Set Color", "sort": "Sort Tasks"}, "card": {"title": "Card", "titles": "Cards", "add": "Add Card", "edit": "Edit Card", "delete": "Delete Card", "move": "Move Card", "duplicate": "Duplicate Card", "archive": "Archive Card", "restore": "Restore Card", "details": "Card Details", "color": "Card Color", "cover": "Cover Image", "labels": "Labels", "members": "Members", "dueDate": "Due Date", "checklist": "Checklist", "attachments": "Attachments", "comments": "Comments", "activity": "Activity Log"}, "actions": {"addColumn": "Add Column", "createColumn": "Create Column", "editColumn": "<PERSON>", "deleteColumn": "Delete Column", "moveColumn": "Move Column", "duplicateColumn": "Duplicate Column", "clearColumn": "Clear Column", "archiveColumn": "Archive Column", "restoreColumn": "<PERSON><PERSON>", "createCard": "Create Card", "editCard": "Edit Card", "deleteCard": "Delete Card", "moveCard": "Move Card", "duplicateCard": "Duplicate Card", "archiveCard": "Archive Card", "restoreCard": "Restore Card", "dragCard": "Drag Card", "dropCard": "Drop Card", "sortCards": "Sort Cards", "filterCards": "Filter Cards", "searchCards": "Search Cards", "selectCards": "Select Cards", "bulkEdit": "Bulk Edit", "bulkMove": "Bulk Move", "bulkDelete": "Bulk Delete", "bulkArchive": "Bulk Archive", "dragColumn": "Drag to reorder column"}, "placeholders": {"columnTitle": "Enter column title", "cardTitle": "Enter card title", "searchCards": "Search cards...", "filterCards": "Filter cards...", "addCard": "Click to add card", "addColumn": "Click to add column", "dragCard": "Drop task here", "dropCard": "Drop card", "emptyColumn": "No tasks", "emptyBoard": "Board is empty"}, "messages": {"columnCreated": "Column created successfully", "columnUpdated": "Column updated successfully", "columnDeleted": "Column deleted successfully", "columnMoved": "Column moved successfully", "columnDuplicated": "Column duplicated successfully", "columnCleared": "Column cleared successfully", "columnArchived": "Column archived successfully", "columnRestored": "Column restored successfully", "cardCreated": "Card created successfully", "cardUpdated": "Card updated successfully", "cardDeleted": "Card deleted successfully", "cardMoved": "Card moved successfully", "cardDuplicated": "Card duplicated successfully", "cardArchived": "Card archived successfully", "cardRestored": "Card restored successfully", "cardsSorted": "Cards sorted successfully", "cardsFiltered": "Cards filtered successfully", "bulkEditSuccess": "B<PERSON>k edit successful", "bulkMoveSuccess": "Bulk move successful", "bulkDeleteSuccess": "Bulk delete successful", "bulkArchiveSuccess": "Bulk archive successful", "columnTitleRequired": "Column title is required", "cardTitleRequired": "Card title is required", "columnNotFound": "Column not found", "cardNotFound": "Card not found", "cannotDeleteColumn": "Cannot delete column", "cannotDeleteCard": "Cannot delete card", "cannotMoveColumn": "Cannot move column", "cannotMoveCard": "Cannot move card", "columnLimitReached": "Column limit reached", "cardLimitReached": "Card limit reached", "noColumnsFound": "No matching columns found", "noCardsFound": "No matching cards found", "loadingBoard": "Loading board...", "savingBoard": "Saving board...", "boardSaved": "Board saved", "boardLoadFailed": "Failed to load board", "boardSaveFailed": "Failed to save board", "columnTitleTooLong": "Column name cannot exceed {{maxLength}} characters", "columnCreateFailed": "Failed to add, please try again"}, "filters": {"allCards": "All Cards", "myCards": "My Cards", "unassignedCards": "Unassigned Cards", "overdueCards": "Overdue Cards", "dueSoonCards": "Due Soon Cards", "completedCards": "Completed Cards", "inProgressCards": "In Progress Cards", "highPriorityCards": "High Priority Cards", "lowPriorityCards": "Low Priority Cards", "recentlyCreatedCards": "Recently Created Cards", "recentlyUpdatedCards": "Recently Updated Cards", "cardsWithAttachments": "Cards with Attachments", "cardsWithComments": "Cards with Comments", "cardsWithLabels": "Cards with Labels", "blockedCards": "Blocked Cards"}, "sorting": {"manual": "Manual Sort", "title": "Sort by Title", "priority": "Sort by Priority", "dueDate": "Sort by Due Date", "createdAt": "Sort by Created Date", "updatedAt": "Sort by Updated Date", "assignee": "Sort by <PERSON><PERSON><PERSON>", "status": "Sort by Status", "progress": "Sort by Progress", "ascending": "Ascending", "descending": "Descending", "manualDesc": "Customize order by dragging", "priorityDesc": "High priority first", "createdDesc": "Newest created first", "titleDesc": "Sort alphabetically"}, "colors": {"default": "<PERSON><PERSON><PERSON>", "red": "Red", "orange": "Orange", "yellow": "Yellow", "green": "Green", "blue": "Blue", "purple": "Purple", "pink": "Pink", "gray": "<PERSON>", "brown": "<PERSON>", "cyan": "<PERSON><PERSON>", "lime": "Lime", "indigo": "Indigo", "teal": "<PERSON><PERSON>", "amber": "Amber", "emerald": "Emerald", "rose": "<PERSON>", "violet": "Violet", "sky": "Sky", "slate": "Slate", "zinc": "Zinc", "selectColor": "Select Color", "preset": "Preset Colors", "custom": "Custom", "soft": "Soft Tones", "vibrant": "Vibrant Colors", "neutral": "Neutral Tones", "gradient": "Gradients", "apply": "Apply Color", "clear": "Clear"}, "themes": {"light": "Light Theme", "dark": "Dark Theme", "cherry": "Cherry Theme", "ocean": "Ocean Theme", "auto": "Follow System"}, "background": {"title": "Board Background", "selectTitle": "Select Board Background Color", "followTheme": "Follow Theme", "followThemeDesc": "Automatically use the default background color of the current theme", "current": "Current Background", "defaultWhite": "De<PERSON><PERSON>", "basicColors": "Basic Colors", "mediumColors": "Medium Saturation Colors", "gradientColors": "Gradient Backgrounds", "resetToDefault": "Reset to De<PERSON>ult"}, "backgroundColors": {"default_white": "De<PERSON><PERSON>", "default_gray": "<PERSON><PERSON><PERSON>", "light_blue": "Light Blue", "light_green": "Light Green", "light_purple": "Light Purple", "light_yellow": "Light Yellow", "light_red": "Light Red", "light_orange": "Light Orange", "light_cyan": "Light Cyan", "light_pink": "Light Pink", "sky_blue": "Sky Blue", "grass_green": "Grass Green", "lavender": "Lavender", "lemon": "Lemon Yellow", "coral": "Coral Red", "apricot": "Apricot Orange", "aqua": "Aqua", "rose": "<PERSON>", "gradient_blue_purple": "Blue Purple Gradient", "gradient_green_cyan": "<PERSON>", "gradient_orange_red": "Orange Red Gradient", "gradient_pink_purple": "Pink Purple Gradient", "gradient_yellow_green": "Yellow Green Gradient"}, "shortcuts": {"enterToSave": "Enter to save", "escToCancel": "Esc to cancel"}}
# 前端看板功能设计文档

## 一、整体看板功能

1.  **项目/看板切换**：
    *   如果支持多项目或多看板，提供切换机制（例如下拉菜单或侧边栏列表）。
    *   可支持多个看板进行切换。

2.  **列 (Columns) 管理**：
    *   **显示固定的列**：例如"待办 (To Do)"、"进行中 (In Progress)"、"测试中 (Testing)"、"已完成 (Done)"。列的名称和数量在初期可以固定。
    *   **自定义列**：允许用户添加、删除、重命名和重新排序看板中的列。
    *   **列背景色**：用户可以通过列右上角的"更多"按钮进入设置面板，选择列的背景色。

3.  **任务卡片创建**：
    *   在特定列的底部或顶部提供"添加卡片"按钮或快捷方式。
    *   点击后弹出创建卡片的表单或模态框。

4.  **任务卡片生成 (通过外部 LLM 与 MCP 服务协同)**：
    *   **源头**: 产品需求文档 (PRD) 可以由支持 MCP 的编辑器 (如 Cursor) 内的大语言模型 (LLM) 生成，或者用户将现有 PRD 提供给该 LLM。
    *   **LLM 初步处理**: 编辑器中的 LLM 负责将 PRD 内容解析并直接转换为结构化的任务数据集 (包含任务标题、描述、预期属性等，格式由 MCP 服务预定义)。
    *   **数据集传输至 MCP 服务**: 该 LLM (作为 MCP 客户端) 通过调用我们 MCP 服务暴露的特定工具/接口，将此结构化任务数据集发送到我们的 MCP 服务。
    *   **MCP 服务处理与前端同步**:
        *   我们的 MCP 服务接收此预定义的任务数据集后，进行处理（例如，验证数据、存入数据库）。
        *   随后，MCP 服务通过 WebSocket 将新创建或更新的任务数据推送给前端看板应用。
    *   **前端展示**: 前端看板应用接收到这些新任务数据后，在相应的列 (例如"待办"列) 自动生成并展示对应的任务卡片。
    *   **前端在此流程中的角色**: 前端看板应用本身不提供用户直接上传 PRD 文档并由前端发起解析的功能。任务的生成是由外部 LLM 主导并通过 MCP 服务流入，前端负责展示和基本交互。

5.  **筛选与搜索**：
    *   **按关键词搜索卡片**：在看板顶部提供搜索框，实时过滤匹配标题或描述的卡片。
    *   **按标签/负责人/优先级等筛选卡片**。

6.  **视图切换**：
    *   **（MVP 核心）卡片看板视图**：标准的列式看板。
    *   **列表视图**：将任务以列表形式展示，可能更适合查看大量任务。
    *   **日历视图**：如果任务有截止日期，可以按日历展示。

7.  **用户认证与授权**：暂不考虑


8.  **设置**：
    *   看板级别的设置，例如看板名称、描述等。
    *   （MVP 可选）应用级别的设置。AI 服务相关的配置（如 API Key）应由后端 MCP 服务或其调用的 AI 微服务管理，前端不直接处理。

## 二、任务卡片 (Task Card) 功能

每个卡片在看板列中以摘要形式展示，点击后可展开查看详情或进入编辑模式。

1.  **卡片摘要展示**：
    *   **任务标题**：清晰展示任务的核心内容。
    *   **任务 ID**：唯一的标识符。
    *   **优先级指示**：例如用不同颜色标记高、中、低优先级。
    *   **截止日期**：如果设置了。
    *   **标签**：简短的分类标签。
    *   **卡片背景色**：用户可以选择卡片的背景色，使视觉上更易区分不同类型的任务。

2.  **卡片拖拽**：
    *   允许用户在不同的列之间拖拽卡片，以改变任务状态。
    *   拖拽完成后，前端调用 MCP 服务的 `update_task` 工具更新任务状态。
    *   （可选）在同一列内拖拽卡片进行排序。

3.  **点击卡片查看/编辑详情**：
    *   点击卡片后，打开一个模态框、侧边栏或扩展区域显示卡片详情。
    *   详情面板应包含以下可编辑内容：
        *   **任务标题 (Title)**
        *   **任务描述 (Description)**：支持富文本或 Markdown 编辑器，详细描述任务需求。
        *   **验收标准 (Acceptance Criteria)**：明确任务完成的标准。
        *   **截止日期 (Due Date)**
        *   **负责人 (Assignee)**
        *   **优先级 (Priority)**
        *   **标签 (Tags)**
        *   **估算工时 (Estimated Effort)**
        *   **实际工时 (Logged Time)**
        *   **卡片背景色 (Card Color)**：提供预设的背景色选项供用户选择。

4.  **卡片内快捷操作 (通常在鼠标悬停或右上角菜单中)**：
    *   **编辑**：快速进入编辑模式。
    *   **删除**：删除任务卡片（需二次确认）。
    *   **复制/克隆**：创建一个与当前卡片内容相似的新卡片。
    *   **归档**：将卡片从看板中移除但保留数据，区别于删除。
    *   **更改背景色**：快速更改卡片背景色的选项。



## 三、实时同步与通知

1.  **WebSocket 连接**：
    *   前端应用启动后，与 MCP 服务建立 WebSocket 连接，订阅任务变更事件 (如 `task_added`, `task_updated`, `task_deleted`)。
    *   当其他用户或通过其他 MCP 客户端（如 Cursor）修改了任务数据，MCP 服务端会通过 WebSocket 推送更新。
    *   前端接收到事件后，实时更新看板和卡片状态，确保数据一致性。


## 四、待考虑/未来迭代

*   **多语言支持**：
    *   初期支持中文（简体）和英文。
    *   需要引入 i18n 库进行文本管理和切换。
*   **主题切换 (深色/浅色模式)**：
    *   提供至少"浅色"和"深色"两种固定主题。
    *   可考虑增加数个精心设计的预设配色方案。
    *   （高级/可选）用户自定义配色方案：
        *   允许用户通过颜色选择器调整应用的核心颜色（如背景、文本、主色调、强调色）。
        *   实现复杂度较高，尤其需要关注用户选择的颜色组合是否满足可访问性标准（如对比度）。
*   **撤销/重做操作**
*   **更高级的过滤和排序选项**
*   **与其他工具的集成 (如日历、代码仓库)**
*   **离线支持 (PWA)**

---

## 五、技术选型

根据项目需求和当前主流技术趋势，前端看板应用拟采用以下技术栈：

*   **UI 框架**: React
*   **构建工具**: Vite
*   **状态管理**: Zustand
*   **桌面应用打包**: Electron
*   **实时通信 (与 MCP 服务交互)**: Socket.IO-client
*   **样式方案**: Tailwind CSS
*   **开发语言**: TypeScript


# 前端界面测试指南

## API密钥管理界面测试

### 前置条件
1. 确保后端服务正在运行 (http://localhost:3000)
2. 确保前端开发服务器正在运行 (http://localhost:5173)
3. 拥有已注册的测试用户账号

### 测试步骤

#### 1. 访问设置页面
1. 登录系统
2. 点击用户头像或导航到用户设置页面
3. 验证是否能看到"API密钥"标签页

**预期结果:**
- ✅ 设置页面应该显示4个标签：个人资料、外观、偏好设置、API密钥
- ✅ API密钥标签应该有钥匙图标
- ✅ 点击API密钥标签应该切换到API密钥管理界面

#### 2. 空状态显示测试
**场景:** 用户没有创建任何API密钥时

**预期结果:**
- ✅ 显示空状态图标（钥匙图标）
- ✅ 显示"暂无API密钥"标题
- ✅ 显示引导文字："创建您的第一个API密钥来开始使用MCP服务"
- ✅ 显示"创建第一个API密钥"按钮

#### 3. 创建API密钥测试

##### 3.1 打开创建对话框
1. 点击"创建新密钥"按钮
2. 验证创建对话框是否正确打开

**预期结果:**
- ✅ 模态对话框应该打开
- ✅ 标题显示"创建API密钥"
- ✅ 包含必要的表单字段：
  - 密钥名称 (必填)
  - 权限选择 (至少选择一个)
  - 高级选项 (可展开)

##### 3.2 表单验证测试
1. 尝试提交空表单
2. 尝试不选择任何权限
3. 尝试输入过长的名称

**预期结果:**
- ✅ 空名称应该显示错误提示
- ✅ 无权限应该显示错误提示
- ✅ 提交按钮应该被禁用直到表单有效

##### 3.3 权限选择测试
1. 测试各个权限复选框
2. 验证权限描述文字

**预期结果:**
- ✅ 默认选中"MCP读取权限"和"MCP写入权限"
- ✅ 每个权限选项都有清晰的描述
- ✅ 权限选择应该响应用户点击

##### 3.4 高级选项测试
1. 点击"高级选项"展开/收起
2. 测试过期时间选择

**预期结果:**
- ✅ 高级选项应该可以展开和收起
- ✅ 过期时间选项包括：永不过期、30天、90天、1年、自定义
- ✅ 选择自定义时应该显示日期时间选择器

##### 3.5 成功创建测试
1. 填写有效的表单数据
2. 提交表单

**预期结果:**
- ✅ 创建对话框关闭
- ✅ 成功对话框打开，显示完整的API密钥
- ✅ 显示安全警告信息
- ✅ 包含复制按钮

#### 4. 成功页面测试

##### 4.1 API密钥显示
1. 验证API密钥格式
2. 测试复制功能

**预期结果:**
- ✅ API密钥格式为：xitools_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
- ✅ 点击复制按钮应该复制密钥到剪贴板
- ✅ 复制成功后按钮状态变为"已复制"

##### 4.2 使用示例测试
1. 切换不同的示例标签 (cURL, JavaScript, Python)
2. 测试代码复制功能

**预期结果:**
- ✅ 三个示例标签应该正常切换
- ✅ 每个示例都包含正确的API密钥
- ✅ 代码复制功能正常工作

##### 4.3 快速入门和链接
1. 验证快速入门步骤显示
2. 检查相关链接

**预期结果:**
- ✅ 显示3个步骤的快速入门指南
- ✅ 相关文档链接正确显示

#### 5. API密钥列表测试

##### 5.1 密钥卡片显示
关闭成功对话框后，验证列表显示

**预期结果:**
- ✅ 新创建的API密钥应该出现在列表中
- ✅ 卡片显示：
  - 密钥名称
  - 状态标签（活跃/已禁用/已过期）
  - 密钥前缀（隐藏敏感部分）
  - 权限标签
  - 创建时间
  - 最后使用时间

##### 5.2 密钥操作测试
1. 点击密钥前缀复制格式
2. 点击展开详情按钮
3. 测试删除功能

**预期结果:**
- ✅ 点击密钥前缀应该复制显示格式到剪贴板
- ✅ 展开详情应该显示使用统计信息
- ✅ 删除按钮应该有确认对话框

##### 5.3 使用统计测试
1. 展开密钥详情
2. 验证统计信息加载

**预期结果:**
- ✅ 统计信息应该正确加载
- ✅ 显示：总请求数、成功请求、失败请求、最常用工具
- ✅ 加载期间显示加载动画

#### 6. 响应式设计测试

##### 6.1 移动端适配
1. 调整浏览器窗口大小到移动端尺寸
2. 测试各种屏幕宽度

**预期结果:**
- ✅ 卡片布局应该适应屏幕宽度
- ✅ 按钮和表单元素应该适当调整
- ✅ 文字和间距应该保持可读性

##### 6.2 平板适配
1. 测试中等屏幕尺寸
2. 验证布局合理性

**预期结果:**
- ✅ 卡片网格应该合理调整列数
- ✅ 对话框大小应该适配屏幕

#### 7. 错误处理测试

##### 7.1 网络错误
1. 断开网络连接
2. 尝试创建API密钥

**预期结果:**
- ✅ 应该显示网络错误提示
- ✅ 不应该导致界面崩溃

##### 7.2 服务器错误
1. 后端返回错误时的处理
2. 验证错误信息显示

**预期结果:**
- ✅ 错误提示应该清晰易懂
- ✅ 用户可以重试操作

#### 8. 性能测试

##### 8.1 加载速度
1. 测试页面切换速度
2. 测试大量API密钥时的性能

**预期结果:**
- ✅ 页面切换应该流畅
- ✅ 列表渲染应该快速

##### 8.2 内存使用
1. 长时间使用界面
2. 多次创建和删除密钥

**预期结果:**
- ✅ 没有明显的内存泄漏
- ✅ 界面保持响应

### 测试检查清单

#### 基础功能 ✅
- [ ] 设置页面API密钥标签显示正确
- [ ] 空状态显示正确
- [ ] 创建对话框正常打开和关闭
- [ ] 表单验证工作正常
- [ ] API密钥成功创建
- [ ] 成功页面显示完整信息
- [ ] 密钥列表正确显示
- [ ] 复制功能正常工作

#### 高级功能 ✅
- [ ] 权限选择功能正常
- [ ] 过期时间设置正常
- [ ] 使用统计正确显示
- [ ] 删除确认对话框正常
- [ ] 使用示例代码正确生成

#### 用户体验 ✅
- [ ] 响应式设计在各种设备上正常
- [ ] 加载状态显示适当
- [ ] 错误处理用户友好
- [ ] 操作反馈及时准确
- [ ] 界面美观且一致

#### 安全性 ✅
- [ ] API密钥只在创建时显示一次
- [ ] 列表中正确隐藏敏感信息
- [ ] 删除操作有确认机制
- [ ] 安全提示信息清晰

### 常见问题排查

#### 1. 页面不显示API密钥标签
- 检查是否正确导入ApiKeyManagement组件
- 检查路由配置是否正确
- 验证用户权限设置

#### 2. 创建对话框无法打开
- 检查Modal组件是否正确导入
- 验证状态管理是否正常
- 检查CSS样式是否正确加载

#### 3. API调用失败
- 验证后端服务是否正在运行
- 检查API端点配置
- 确认用户认证状态

#### 4. 样式显示异常
- 确认api-keys.css已正确导入
- 检查CSS变量定义
- 验证主题配置

### 浏览器兼容性

#### 支持的浏览器
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

#### 测试建议
1. 在主要浏览器中测试核心功能
2. 验证复制到剪贴板功能在所有浏览器中正常
3. 确认日期时间选择器兼容性

---

**测试完成标准:**
- 所有基础功能测试项目通过
- 所有高级功能测试项目通过
- 响应式设计在主流设备上正常
- 错误处理机制完善
- 用户体验流畅自然
#!/usr/bin/env node

/**
 * MCP认证端点测试脚本
 * 用于验证/mcp-auth端点是否正确实现了streamable-http协议
 */

const https = require('https');

const API_KEY = 'xitool_e6604a2ba5f29d6f8ca3eb2c3e4b4077c52eceb301fab21afb4101bdaf217551';
const BASE_URL = 'https://xitools.furdow.com';

// 测试健康检查端点
async function testHealthCheck() {
  console.log('🔍 测试健康检查端点...');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'xitools.furdow.com',
      port: 443,
      path: '/mcp-auth/health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`✅ 健康检查状态: ${res.statusCode}`);
        console.log(`📄 响应: ${data}`);
        resolve({ status: res.statusCode, data });
      });
    });

    req.on('error', (error) => {
      console.error('❌ 健康检查失败:', error.message);
      reject(error);
    });

    req.end();
  });
}

// 测试GET端点（SSE）
async function testGetEndpoint() {
  console.log('\n🔍 测试GET端点（SSE）...');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'xitools.furdow.com',
      port: 443,
      path: '/mcp-auth',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Accept': 'text/event-stream',
      }
    };

    const req = https.request(options, (res) => {
      console.log(`✅ GET端点状态: ${res.statusCode}`);
      console.log(`📄 Content-Type: ${res.headers['content-type']}`);
      
      if (res.statusCode === 200) {
        let dataReceived = false;
        res.on('data', (chunk) => {
          if (!dataReceived) {
            console.log(`📡 SSE数据: ${chunk.toString().substring(0, 200)}...`);
            dataReceived = true;
            resolve({ status: res.statusCode, contentType: res.headers['content-type'] });
          }
        });
        
        // 5秒后关闭连接
        setTimeout(() => {
          req.destroy();
          if (!dataReceived) {
            resolve({ status: res.statusCode, contentType: res.headers['content-type'] });
          }
        }, 5000);
      } else {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          console.log(`📄 错误响应: ${data}`);
          resolve({ status: res.statusCode, data });
        });
      }
    });

    req.on('error', (error) => {
      console.error('❌ GET端点测试失败:', error.message);
      reject(error);
    });

    req.end();
  });
}

// 测试POST端点（初始化）
async function testInitialize() {
  console.log('\n🔍 测试POST端点（初始化）...');
  
  const initRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2025-03-26',
      capabilities: {
        roots: { listChanged: true },
        sampling: {}
      },
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };

  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(initRequest);
    
    const options = {
      hostname: 'xitools.furdow.com',
      port: 443,
      path: '/mcp-auth',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`✅ 初始化状态: ${res.statusCode}`);
        console.log(`📄 Content-Type: ${res.headers['content-type']}`);
        console.log(`📄 响应: ${data}`);
        resolve({ status: res.statusCode, data, contentType: res.headers['content-type'] });
      });
    });

    req.on('error', (error) => {
      console.error('❌ 初始化测试失败:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始MCP认证端点测试...\n');
  
  try {
    // 测试健康检查
    await testHealthCheck();
    
    // 测试GET端点
    await testGetEndpoint();
    
    // 测试POST初始化
    await testInitialize();
    
    console.log('\n✅ 所有测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
runTests();

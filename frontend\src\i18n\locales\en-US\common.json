{"loading": "Loading...", "locale": "en-US", "app": {"name": "XItools", "title": "Smart Task Board", "description": "AI-powered intelligent task management tool"}, "navigation": {"board": "Board", "list": "List", "calendar": "Calendar", "settings": "Settings", "workspace": "Workspace", "project": "Project", "createProject": "Create Project", "createBoard": "Create Board", "createProjectDescription": "Projects can contain multiple boards, suitable for organizing related task collections", "createBoardDescription": "Create an independent task board directly under the workspace", "selectCreateType": "Create new in \"{{workspaceName}}\"", "selectCreateTypeDescription": "Please select the type to create:", "deleteWorkspaceTitle": "Delete Workspace", "deleteWorkspaceMessage": "Are you sure you want to delete workspace \"{{workspaceName}}\"? This action cannot be undone and will delete all projects and boards under it.", "deleteProjectTitle": "Delete Project", "deleteProjectMessage": "Are you sure you want to delete project \"{{projectName}}\"? This action cannot be undone and will delete all boards under it.", "deleteBoardTitle": "Delete Board", "deleteBoardMessage": "Are you sure you want to delete board \"{{boardName}}\"? This action cannot be undone.", "cannotDeleteBoardTitle": "Cannot Delete Board", "cannotDeleteBoardMessage": "Board \"{{boardName}}\" contains {{taskCount}} tasks. Please delete or move all tasks before deleting the board."}, "actions": {"create": "Create", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "add": "Add", "remove": "Remove", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "copy": "Copy", "move": "Move", "duplicate": "Duplicate", "archive": "Archive", "restore": "Rest<PERSON>", "export": "Export", "import": "Import", "upload": "Upload", "download": "Download", "preview": "Preview", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "expand": "Expand", "collapse": "Collapse", "maximize": "Maximize", "minimize": "Minimize", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen", "reconnect": "Reconnect", "clearAll": "Clear All", "more": "More Actions", "retry": "Retry", "loading": "Loading...", "actions": "Actions", "clearSearch": "Clear Search", "clearFilters": "Clear Filters"}, "status": {"loading": "Loading...", "saving": "Saving...", "saved": "Saved", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "timeout": "Timeout", "offline": "Offline", "online": "Online", "connected": "Connected", "disconnected": "Disconnected", "syncing": "Syncing", "synced": "Synced"}, "time": {"now": "Just now", "minutesAgo": "{{count}} minute ago", "minutesAgo_other": "{{count}} minutes ago", "hoursAgo": "{{count}} hour ago", "hoursAgo_other": "{{count}} hours ago", "daysAgo": "{{count}} day ago", "daysAgo_other": "{{count}} days ago", "weeksAgo": "{{count}} week ago", "weeksAgo_other": "{{count}} weeks ago", "monthsAgo": "{{count}} month ago", "monthsAgo_other": "{{count}} months ago", "yearsAgo": "{{count}} year ago", "yearsAgo_other": "{{count}} years ago", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "lastWeek": "Last week", "nextWeek": "Next week", "thisMonth": "This month", "lastMonth": "Last month", "nextMonth": "Next month", "thisYear": "This year", "lastYear": "Last year", "nextYear": "Next year"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Must be at least {{count}} characters", "maxLength": "Must be no more than {{count}} characters", "pattern": "Invalid format", "numeric": "Please enter a number", "positive": "Please enter a positive number", "integer": "Please enter an integer", "url": "Please enter a valid URL", "date": "Please enter a valid date", "time": "Please enter a valid time", "phone": "Please enter a valid phone number"}, "messages": {"noData": "No data available", "noResults": "No results found", "noConnection": "Network connection failed", "serverError": "Server error", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "notFound": "Page not found", "timeout": "Request timeout", "unknownError": "Unknown error", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "saveSuccess": "Save successful", "saveFailed": "Save failed", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "updateSuccess": "Update successful", "updateFailed": "Update failed", "createSuccess": "Create successful", "createFailed": "Create failed"}, "placeholders": {"search": "Search...", "enterText": "Enter text", "selectOption": "Select an option", "chooseFile": "Choose file", "enterEmail": "Enter email", "enterPassword": "Enter password", "enterName": "Enter name", "enterTitle": "Enter title", "enterDescription": "Enter description", "enterUrl": "Enter URL", "enterDate": "Select date", "enterTime": "Select time"}}
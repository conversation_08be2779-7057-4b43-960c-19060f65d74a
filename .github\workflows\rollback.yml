# XItools 回滚工作流
# 简化版回滚，用于紧急回滚到上一个稳定版本

name: 紧急回滚

on:
  workflow_dispatch:
    inputs:
      environment:
        description: '回滚环境'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging

jobs:
  # 执行回滚
  rollback:
    name: 执行回滚
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}

    steps:
    - name: 设置 SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: 执行回滚操作
      run: |
        echo "🔄 开始回滚操作..."

        ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
        cd /opt/xitools

        # 获取当前版本
        CURRENT_VERSION=$(readlink current | sed 's|releases/||' 2>/dev/null || echo "unknown")
        echo "📦 当前版本: $CURRENT_VERSION"

        # 自动选择上一个版本
        TARGET_VERSION=$(cd releases && ls -t | sed -n '2p' 2>/dev/null || echo "")

        if [ -z "$TARGET_VERSION" ]; then
          echo "❌ 无法找到可回滚的版本"
          exit 1
        fi

        echo "🎯 回滚到版本: $TARGET_VERSION"

        # 停止当前服务
        if [ -f current/docker-compose.prod.yml ]; then
          echo "⏹️ 停止当前服务..."
          docker-compose -f current/docker-compose.prod.yml down || true
        fi

        # 切换版本
        echo "🔄 切换到目标版本..."
        ln -sfn releases/$TARGET_VERSION current

        # 启动服务
        cd current
        echo "🚀 启动服务..."
        docker-compose -f docker-compose.prod.yml up -d

        echo "✅ 回滚操作完成"
        EOF

    - name: 基础健康检查
      run: |
        echo "🏥 等待服务启动并进行健康检查..."
        sleep 30

        # 简单的健康检查
        for i in {1..5}; do
          if curl -f -s --connect-timeout 5 "https://xitools.furdow.com/" > /dev/null; then
            echo "✅ 回滚后健康检查通过"
            break
          else
            echo "⏳ 等待服务启动... ($i/5)"
            if [ $i -eq 5 ]; then
              echo "⚠️ 健康检查超时，但回滚已完成"
            fi
            sleep 10
          fi
        done

    - name: 回滚完成通知
      if: always()
      run: |
        echo "📢 回滚操作完成"
        echo "环境: ${{ github.event.inputs.environment }}"
        echo "操作人: ${{ github.actor }}"
        echo "回滚时间: $(date)"

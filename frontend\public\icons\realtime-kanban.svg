<!-- 实时协作看板图标 - 表示多人实时协作的看板系统 -->
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="12" cy="12" r="11" fill="url(#kanbanGradient)" opacity="0.1"/>

  <!-- 看板主体 -->
  <rect x="4" y="5" width="16" height="12" rx="1.5" fill="none" stroke="currentColor" stroke-width="1.2" opacity="0.7"/>

  <!-- 看板列分隔线 -->
  <line x1="9" y1="5" x2="9" y2="17" stroke="currentColor" stroke-width="0.8" opacity="0.5"/>
  <line x1="15" y1="5" x2="15" y2="17" stroke="currentColor" stroke-width="0.8" opacity="0.5"/>

  <!-- 列标题 -->
  <rect x="4.5" y="6" width="4" height="0.8" rx="0.2" fill="currentColor" opacity="0.6"/>
  <rect x="9.5" y="6" width="5" height="0.8" rx="0.2" fill="currentColor" opacity="0.6"/>
  <rect x="15.5" y="6" width="4" height="0.8" rx="0.2" fill="currentColor" opacity="0.6"/>

  <!-- 任务卡片 -->
  <rect x="5" y="8" width="3.5" height="1.2" rx="0.3" fill="currentColor" opacity="0.8"/>
  <rect x="5" y="9.5" width="3.5" height="1.2" rx="0.3" fill="currentColor" opacity="0.6"/>

  <rect x="10" y="8" width="4.5" height="1.2" rx="0.3" fill="currentColor" opacity="0.8"/>
  <rect x="10" y="9.5" width="4.5" height="1.2" rx="0.3" fill="currentColor" opacity="0.6"/>
  <rect x="10" y="11" width="4.5" height="1.2" rx="0.3" fill="currentColor" opacity="0.4"/>

  <rect x="16" y="8" width="3.5" height="1.2" rx="0.3" fill="currentColor" opacity="0.8"/>

  <!-- 实时协作用户头像 -->
  <circle cx="6" cy="3" r="1.2" fill="currentColor" opacity="0.8"/>
  <circle cx="6" cy="3" r="0.6" fill="none" stroke="white" stroke-width="0.3"/>

  <circle cx="12" cy="2.5" r="1.2" fill="currentColor" opacity="0.7"/>
  <circle cx="12" cy="2.5" r="0.6" fill="none" stroke="white" stroke-width="0.3"/>

  <circle cx="18" cy="3" r="1.2" fill="currentColor" opacity="0.6"/>
  <circle cx="18" cy="3" r="0.6" fill="none" stroke="white" stroke-width="0.3"/>

  <!-- 实时状态指示器 -->
  <circle cx="7.2" cy="2.2" r="0.4" fill="#4CAF50">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="13.2" cy="1.7" r="0.4" fill="#4CAF50">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="0.7s"/>
  </circle>
  <circle cx="19.2" cy="2.2" r="0.4" fill="#4CAF50">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite" begin="1.4s"/>
  </circle>

  <!-- 实时同步波纹 -->
  <circle cx="12" cy="12" r="8" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.3">
    <animate attributeName="r" values="8;12;8" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.3;0;0.3" dur="3s" repeatCount="indefinite"/>
  </circle>

  <!-- 数据流动指示 -->
  <path d="M 6 4.5 Q 12 7 18 4.5" stroke="currentColor" stroke-width="0.8" fill="none" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.5s" repeatCount="indefinite"/>
  </path>

  <!-- 移动的任务卡片 -->
  <rect x="10" y="12.5" width="4.5" height="1.2" rx="0.3" fill="currentColor" opacity="0.5">
    <animateTransform attributeName="transform" type="translate"
                      values="0,0; 6,0; 6,0; 0,0"
                      dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.5;0.9;0.9;0.5" dur="4s" repeatCount="indefinite"/>
  </rect>

  <!-- WebSocket连接线 -->
  <path d="M 2 19 Q 12 21 22 19" stroke="currentColor" stroke-width="0.6" fill="none" opacity="0.3">
    <animate attributeName="opacity" values="0.3;0.7;0.3" dur="3s" repeatCount="indefinite"/>
  </path>

  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="kanbanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:currentColor;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:currentColor;stop-opacity:0.05"/>
    </linearGradient>
  </defs>
</svg>

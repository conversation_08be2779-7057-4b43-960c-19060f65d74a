<!-- 多视图管理图标 - 表示看板、列表、日历三种视图模式 -->
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="12" cy="12" r="11" fill="url(#multiViewGradient)" opacity="0.1"/>
  
  <!-- 中央切换控制器 -->
  <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="1.2" opacity="0.8"/>
  <circle cx="12" cy="12" r="1.5" fill="currentColor" opacity="0.9"/>
  
  <!-- 看板视图 (左上) -->
  <g transform="translate(4, 4)">
    <rect x="0" y="0" width="6" height="6" rx="0.8" fill="none" stroke="currentColor" stroke-width="0.8" opacity="0.6"/>
    <!-- 看板列 -->
    <rect x="0.5" y="1" width="1.5" height="4" rx="0.2" fill="currentColor" opacity="0.4"/>
    <rect x="2.25" y="1" width="1.5" height="4" rx="0.2" fill="currentColor" opacity="0.4"/>
    <rect x="4" y="1" width="1.5" height="4" rx="0.2" fill="currentColor" opacity="0.4"/>
    <!-- 卡片 -->
    <rect x="0.7" y="1.3" width="1.1" height="0.6" rx="0.1" fill="currentColor" opacity="0.8"/>
    <rect x="2.45" y="1.3" width="1.1" height="0.6" rx="0.1" fill="currentColor" opacity="0.8"/>
    <rect x="2.45" y="2.1" width="1.1" height="0.6" rx="0.1" fill="currentColor" opacity="0.6"/>
  </g>
  
  <!-- 列表视图 (右上) -->
  <g transform="translate(14, 4)">
    <rect x="0" y="0" width="6" height="6" rx="0.8" fill="none" stroke="currentColor" stroke-width="0.8" opacity="0.6"/>
    <!-- 列表行 -->
    <rect x="0.5" y="1" width="5" height="0.8" rx="0.2" fill="currentColor" opacity="0.7"/>
    <rect x="0.5" y="2" width="5" height="0.8" rx="0.2" fill="currentColor" opacity="0.5"/>
    <rect x="0.5" y="3" width="5" height="0.8" rx="0.2" fill="currentColor" opacity="0.5"/>
    <rect x="0.5" y="4" width="5" height="0.8" rx="0.2" fill="currentColor" opacity="0.3"/>
    <!-- 复选框 -->
    <circle cx="1" cy="1.4" r="0.15" fill="currentColor" opacity="0.8"/>
    <circle cx="1" cy="2.4" r="0.15" fill="currentColor" opacity="0.6"/>
    <circle cx="1" cy="3.4" r="0.15" fill="currentColor" opacity="0.6"/>
  </g>
  
  <!-- 日历视图 (下方) -->
  <g transform="translate(9, 14)">
    <rect x="0" y="0" width="6" height="6" rx="0.8" fill="none" stroke="currentColor" stroke-width="0.8" opacity="0.6"/>
    <!-- 日历网格 -->
    <line x1="0.5" y1="1.5" x2="5.5" y2="1.5" stroke="currentColor" stroke-width="0.3" opacity="0.5"/>
    <line x1="2" y1="1" x2="2" y2="5.5" stroke="currentColor" stroke-width="0.3" opacity="0.5"/>
    <line x1="3.5" y1="1" x2="3.5" y2="5.5" stroke="currentColor" stroke-width="0.3" opacity="0.5"/>
    <line x1="0.5" y1="3" x2="5.5" y2="3" stroke="currentColor" stroke-width="0.3" opacity="0.5"/>
    <line x1="0.5" y1="4.5" x2="5.5" y2="4.5" stroke="currentColor" stroke-width="0.3" opacity="0.5"/>
    <!-- 日期点 -->
    <circle cx="1.25" cy="2.25" r="0.2" fill="currentColor" opacity="0.8"/>
    <circle cx="2.75" cy="2.25" r="0.2" fill="currentColor" opacity="0.6"/>
    <circle cx="4.25" cy="3.75" r="0.2" fill="currentColor" opacity="0.7"/>
    <circle cx="1.25" cy="3.75" r="0.2" fill="currentColor" opacity="0.5"/>
  </g>
  
  <!-- 连接线 -->
  <line x1="7" y1="7" x2="10.5" y2="10.5" stroke="currentColor" stroke-width="0.8" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
  </line>
  <line x1="17" y1="7" x2="13.5" y2="10.5" stroke="currentColor" stroke-width="0.8" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite" begin="1s"/>
  </line>
  <line x1="12" y1="17" x2="12" y2="14.5" stroke="currentColor" stroke-width="0.8" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite" begin="2s"/>
  </line>
  
  <!-- 旋转动画的中央指示器 -->
  <g transform-origin="12 12">
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 12 12; 120 12 12; 240 12 12; 360 12 12" 
                      dur="6s" repeatCount="indefinite"/>
    <line x1="12" y1="10.5" x2="12" y2="9" stroke="currentColor" stroke-width="1.5" opacity="0.9"/>
  </g>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="multiViewGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:currentColor;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:currentColor;stop-opacity:0.05"/>
    </linearGradient>
  </defs>
</svg>

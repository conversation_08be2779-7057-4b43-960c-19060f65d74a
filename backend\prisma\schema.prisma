// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/.prisma/client"
  binaryTargets = ["native", "windows", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// 用户认证相关模型
// ================================

// 用户模型 - 用户基本信息
model User {
  id           String    @id @default(uuid())
  username     String    @unique // 用户名，唯一
  email        String    @unique // 邮箱，唯一
  passwordHash String    // 加密后的密码
  avatar       String?   // 头像URL，可选
  bio          String?   // 个人简介，可选
  role         String    @default("user") // 用户角色，默认为普通用户
  isActive     Boolean   @default(true) // 账户是否激活
  lastLoginAt  DateTime? // 最后登录时间
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // 关联关系
  sessions   UserSession[] // 用户会话
  apiKeys    UserApiKey[]  // API密钥
  mcpLogs    McpUsageLog[] // MCP使用日志
  workspaces Workspace[]   // 拥有的工作区
  projects   Project[]     // 拥有的项目
  boards     Board[]       // 拥有的看板
  tasks      Task[]        // 拥有的任务
  tags       Tag[]         // 拥有的标签

  @@index([email])
  @@index([username])
  @@index([isActive])
  @@index([role])
}

// 用户会话模型 - 会话管理
model UserSession {
  id        String    @id @default(uuid())
  userId    String    // 关联用户
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  tokenHash String    @unique // token哈希值
  expiresAt DateTime  // 过期时间
  isRevoked Boolean   @default(false) // 是否已撤销
  userAgent String?   // 用户代理信息
  ipAddress String?   // IP地址
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@index([userId])
  @@index([tokenHash])
  @@index([expiresAt])
  @@index([isRevoked])
}

// 用户API密钥模型 - MCP认证
model UserApiKey {
  id          String    @id @default(uuid())
  userId      String    // 关联用户
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  name        String    // 密钥名称，便于用户识别
  apiKey      String    @unique // API密钥值
  keyPrefix   String    // 密钥前缀，用于展示
  permissions String[]  // 权限列表，如["mcp:read", "mcp:write"]
  lastUsedAt  DateTime? // 最后使用时间
  lastUsedIp  String?   // 最后使用IP
  expiresAt   DateTime? // 过期时间，NULL表示永不过期
  isActive    Boolean   @default(true) // 是否激活
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联关系
  mcpLogs     McpUsageLog[] // 使用日志

  @@index([userId])
  @@index([apiKey])
  @@index([isActive, expiresAt])
  @@index([keyPrefix])
}

// MCP使用日志模型 - 审计和监控
model McpUsageLog {
  id              String      @id @default(uuid())
  apiKeyId        String      // 关联API密钥
  apiKey          UserApiKey  @relation(fields: [apiKeyId], references: [id], onDelete: Cascade)
  userId          String      // 关联用户
  user            User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  toolName        String      // 调用的工具名称
  requestParams   Json?       // 请求参数（JSON格式）
  responseStatus  Int?        // HTTP响应状态码
  errorMessage    String?     // 错误信息
  ipAddress       String?     // 请求IP地址
  userAgent       String?     // 用户代理
  executionTimeMs Int?        // 执行时间（毫秒）
  createdAt       DateTime    @default(now())

  @@index([userId, createdAt])
  @@index([apiKeyId, createdAt])
  @@index([toolName])
  @@index([responseStatus])
}

// 用户角色模型 - 为团队协作预留
model UserRole {
  id          String   @id @default(uuid())
  name        String   @unique // 角色名称，如 "admin", "member", "viewer"
  displayName String   // 角色显示名称
  description String?  // 角色描述
  permissions String[] // 权限列表，JSON数组格式
  isSystem    Boolean  @default(false) // 是否为系统角色
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([name])
  @@index([isSystem])
}

// 工作区模型 - 顶层容器
model Workspace {
  id          String    @id @default(uuid())
  name        String    // 工作区名称
  description String?   // 工作区描述
  isDefault   Boolean   @default(false) // 是否为默认工作区

  // 用户关联
  ownerId     String    // 工作区所有者
  owner       User      @relation(fields: [ownerId], references: [id], onDelete: Cascade)

  // 关联关系
  projects    Project[] // 包含的项目
  boards      Board[]   // 直接在工作区下的看板
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([ownerId])
  @@index([isDefault])
  @@index([ownerId, isDefault]) // 复合索引，用于查询用户的默认工作区
}

// 项目模型 - 项目级容器
model Project {
  id          String    @id @default(uuid())
  name        String    // 项目名称
  description String?   // 项目描述
  color       String?   // 项目主题色
  icon        String?   // 项目图标

  // 层级关系
  workspaceId String    // 所属工作区
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  // 用户关联
  ownerId     String    // 项目所有者
  owner       User      @relation(fields: [ownerId], references: [id], onDelete: Cascade)

  // 关联关系
  boards      Board[]   // 项目下的看板
  order       Int       @default(0) // 在工作区中的排序
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([workspaceId])
  @@index([ownerId])
  @@index([order])
  @@index([ownerId, workspaceId]) // 复合索引，用于查询用户在特定工作区的项目
}

// 看板模型
model Board {
  id          String        @id @default(uuid())
  name        String        // 看板名称
  description String?       // 看板描述
  color       String?       // 看板主题色
  icon        String?       // 看板图标

  // 层级关系 - 看板可以属于工作区或项目
  workspaceId String?       // 直接属于工作区
  workspace   Workspace?    @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  projectId   String?       // 属于项目
  project     Project?      @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 用户关联
  ownerId     String        // 看板所有者
  owner       User          @relation(fields: [ownerId], references: [id], onDelete: Cascade)

  // 关联关系
  columns     BoardColumn[] // 看板列
  tasks       Task[]        // 看板任务
  order       Int           @default(0) // 排序
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@index([workspaceId])
  @@index([projectId])
  @@index([ownerId])
  @@index([order])
  @@index([ownerId, workspaceId]) // 复合索引，用于查询用户在特定工作区的看板
  @@index([ownerId, projectId])   // 复合索引，用于查询用户在特定项目的看板
}

// 看板列模型 - 更新关联关系
model BoardColumn {
  id         String   @id @default(uuid())
  name       String   // 列名称，如"待办"、"进行中"、"已完成"
  order      Int      // 列的排序顺序（移除unique约束，因为现在是按看板分组）
  color      String?  // 列的背景色（可选）
  sortOption String?  @default("manual") // 列的排序方式：manual, priority, created_asc, created_desc, title_asc, title_desc, due_date
  isDefault  Boolean  @default(false) // 是否为默认列
  boardId    String   // 所属看板
  board      Board    @relation(fields: [boardId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now()) @updatedAt

  @@unique([boardId, order]) // 在同一看板内order唯一
  @@index([boardId])
  @@index([order])
}

// 任务模型
model Task {
  id                 String   @id @default(uuid())
  title              String
  description        String   @default("")
  status             String   // 对应BoardColumn的id
  priority           String?
  dueDate            DateTime?
  assignee           String?
  color              String?  // 任务卡片颜色（可选）
  parentId           String?
  acceptanceCriteria String   @default("")
  estimatedEffort    Float?
  loggedTime         Float?
  sortOrder          Int      @default(0) // 用于列内排序

  // 层级关系
  boardId            String   // 所属看板
  board              Board    @relation(fields: [boardId], references: [id], onDelete: Cascade)

  // 用户关联
  ownerId            String   // 任务所有者
  owner              User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)

  // 关联关系
  tags               Tag[]    @relation("TaskToTag")

  // 自引用关系定义 - 父子任务
  parent   Task?  @relation("ParentChild", fields: [parentId], references: [id], onDelete: SetNull)
  subTasks Task[] @relation("ParentChild")

  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now()) @updatedAt

  @@index([status])
  @@index([parentId])
  @@index([boardId])
  @@index([ownerId])
  @@index([ownerId, boardId]) // 复合索引，用于查询用户在特定看板的任务
  @@index([ownerId, status])  // 复合索引，用于查询用户特定状态的任务
}

// 标签模型
model Tag {
  id        String   @id @default(uuid())
  name      String   // 标签名称（移除unique约束，因为现在按用户分组）

  // 用户关联
  ownerId   String   // 标签所有者
  owner     User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)

  // 关联关系
  tasks     Task[]   @relation("TaskToTag")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([ownerId, name]) // 同一用户下标签名称唯一
  @@index([ownerId])
}

// MCP会话模型
model MCPSession {
  id        String       @id @default(uuid())
  sessionId String       @unique
  modelId   String
  status    String       @default("active") // active, archived
  messages  MCPMessage[]
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
}

// MCP消息模型
model MCPMessage {
  id        String     @id @default(uuid())
  sessionId String
  role      String     // user, assistant
  content   String     @db.Text
  session   MCPSession @relation(fields: [sessionId], references: [sessionId], onDelete: Cascade)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
}

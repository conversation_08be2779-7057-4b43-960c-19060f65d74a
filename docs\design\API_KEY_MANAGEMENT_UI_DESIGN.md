# XItools API密钥管理界面设计方案

本方案旨在为XItools设计一个安全、清晰且高效的API密钥管理界面。设计遵循业界最佳实践（如GitHub、Stripe），确保用户在管理敏感的API密钥时拥有良好的体验。

## 1. 设计原则

- **安全第一 (Security First)**：密钥生成后只完整显示一次。这是最重要的安全原则。
- **清晰明确 (Clarity)**：用户必须清楚地了解每个密钥的用途、权限、状态和活动历史。
- **高效操作 (Efficiency)**：常用操作（如创建、复制、撤销）应简单直观，并提供辅助工具（如配置代码片段）来降低用户的使用门槛。

## 2. 页面布局和核心组件

API密钥管理页面位于用户设置下，采用卡片式列表布局，清晰地展示每一个密钥的信息。

**页面路径**: `/settings/api-keys`

### 主界面设计

```
┌──────────────────────────────────────────────────────────┐
│                                                          │
│  API密钥管理                                [ + 生成新密钥 ] │
│  -------------------------------------------------------   │
│                                                          │
│  通过API密钥，您可以授权外部应用（如Cursor）安全地访问您   │
│  在XItools中的数据，而无需暴露您的账户密码。             │
│                                                          │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ [密钥名称] Cursor IDE Key                           │ │
│  │ [前缀]   xitool_aB1cDeF...                         │ │
│  │ [权限]   读取, 写入                                │ │
│  │ [上次使用] 3小时前 (从 123.45.67.89)                │ │
│  │ [创建于]   2025年7月7日                            │ │
│  │ [过期时间] 永不                                     │ │
│  │                                     [编辑] [撤销]   │ │
│  └─────────────────────────────────────────────────────┘ │
│                                                          │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ [密钥名称] 开发测试脚本                             │ │
│  │ [前缀]   xitool_zY9xWvU...                         │ │
│  │ [权限]   只读                                      │ │
│  │ [上次使用] 从未使用过                                │ │
│  │ [创建于]   2025年7月5日                            │ │
│  │ [过期时间] 2025年8月5日                             │ │
│  │                                     [编辑] [撤销]   │ │
│  └─────────────────────────────────────────────────────┘ │
│                                                          │
└──────────────────────────────────────────────────────────┘
```

### 组件详解

- **“生成新密钥”按钮**: 页面主操作按钮，引导用户创建新密钥。
- **密钥信息卡片**:
    - **密钥名称 (Name)**: 用户自定义的标识，方便管理。
    - **前缀 (Prefix)**: 显示密钥的开头部分，用于识别，绝不显示完整密钥。
    - **权限 (Permissions)**: 使用标签清晰展示该密钥拥有的权限（如 `读取`, `写入`）。
    - **上次使用 (Last Used)**: 显示相对时间及来源IP，帮助用户识别不活跃或异常的密钥。
    - **创建/过期时间 (Created/Expires)**: 明确密钥的生命周期。
    - **操作按钮**:
        - **编辑 (Edit)**: 允许用户修改密钥的“名称”或更新其过期时间。
        - **撤销 (Revoke/Delete)**: 危险操作，用红色突出，并需要二次确认。

## 3. 核心用户流程

### 流程一：生成新密钥

点击 **[+ 生成新密钥]** 后，弹出一个模态框（Modal）以收集必要信息。

```
┌───────────────────────────────────────────────┐
│                  生成新的API密钥                  │
├───────────────────────────────────────────────┤
│                                               │
│  名称 (必填)                                  │
│  ┌───────────────────────────────────────────┐  │
│  │ 例如：Cursor个人电脑                     │  │
│  └───────────────────────────────────────────┘  │
│                                               │
│  权限范围                                     │
│  [x] 读取 (允许列出、查看任务和看板列)        │
│  [x] 写入 (允许创建、更新、删除任务和看板列)  │
│                                               │
│  过期时间                                     │
│  (•) 永不   ( ) 7天   ( ) 30天   ( ) 自定义... │
│                                               │
├───────────────────────────────────────────────┤
│                      [生成密钥] [取消]          │
└───────────────────────────────────────────────┘
```

- **名称**: 必填项，引导用户为密钥起一个有意义的名字。
- **权限**: 使用复选框，并清晰地解释每个权限的作用。
- **过期时间**: 提供常用选项和自定义功能，增强安全性。

### 流程二：显示生成的密钥（安全关键点）

点击 **[生成密钥]** 后，模态框内容更新，**这是唯一一次显示完整密钥的机会**。

```
┌──────────────────────────────────────────────────────────┐
│                  API密钥已生成                             │
├──────────────────────────────────────────────────────────┤
│                                                          │
│  ⚠️ 请立即复制您的新API密钥。这是它唯一一次被显示。       │
│                                                          │
│  ┌───────────────────────────────────────────────────┐   │
│  │ xitool_aB1cDeF...gH2iJkL3mN4oP5qR6sT7uV8wX9yZ0      [复制] │
│  └───────────────────────────────────────────────────┘   │
│                                                          │
│  👇 将其用于您的Cursor `settings.json` 文件:             │
│  ┌───────────────────────────────────────────────────┐   │
│  │ {                                                [复制配置] │
│  │   "mcpServers": {                                │   │
│  │     "xitools": {                                 │   │
│  │       ...                                        │   │
│  │       "env": {                                   │   │
│  │         "XITOOLS_API_KEY": "xitool_aB1cDeF..."   │   │
│  │       }                                          │   │
│  │     }                                            │   │
│  │   }                                              │   │
│  │ }                                                │   │
│  └───────────────────────────────────────────────────┘   │
│                                                          │
├──────────────────────────────────────────────────────────┤
│                                                 [完成]   │
└──────────────────────────────────────────────────────────┘
```

- **醒目的警告**: 强调密钥的唯一性和重要性。
- **一键复制密钥**: 核心功能，减少用户出错的概率。
- **提供配置示例 (UX亮点)**: 直接生成用户需要的JSON配置片段，并填入新密钥，极大地降低了用户的使用门槛。
- **完成按钮**: 关闭对话框，返回密钥列表。

### 流程三：撤销密钥

点击 **[撤销]** 后，弹出一个强确认对话框，防止用户误操作。

```
┌───────────────────────────────────────────┐
│              确认撤销密钥？                 │
├───────────────────────────────────────────┤
│                                           │
│  您确定要撤销密钥 “Cursor IDE Key” 吗？   │
│  此操作无法撤销，所有使用此密钥的应用将   │
│  立即失去访问权限。                       │
│                                           │
│  为防止误操作，请输入密钥名称以确认:      │
│  ┌───────────────────────────────────┐      │
│  │ Cursor IDE Key                    │      │
│  └───────────────────────────────────┘      │
│                                           │
├───────────────────────────────────────────┤
│           [确认撤销] [取消]               │
└───────────────────────────────────────────┘
```

- **危险操作警告**: 明确告知用户此操作的后果。
- **输入名称确认**: 要求用户输入密钥名称进行二次确认，这是比简单点击“是”更安全的设计模式，能有效防止意外删除。

## 4. 总结

该设计方案为XItools提供了一个功能完备、安全可靠且用户友好的API密钥管理界面。它通过清晰的布局、流畅的操作流程和到位的安全提示，帮助用户自信地管理应用访问权限。

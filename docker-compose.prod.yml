# XItools 生产环境 Docker Compose 配置
# 使用预构建的镜像进行部署

services:
  # PostgreSQL数据库服务
  postgres:
    image: ghcr.io/justsoow/xitools/postgres:14
    container_name: xitools-postgres-prod
    restart: always
    env_file:
      - .env.production
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: xitools
    ports:
      - "5433:5432"  # 使用不同端口避免与开发环境冲突
    volumes:
      - pg_data_prod:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - xitools-network

  # XItools后端服务
  backend:
    image: ghcr.io/justsoow/xitools/backend:latest
    container_name: xitools-backend-prod
    restart: always
    env_file:
      - .env.production
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-postgres}@postgres:5432/xitools
      - CORS_ORIGINS=${CORS_ORIGINS:-http://xitools.furdow.com,https://xitools.furdow.com,http://localhost:8080,http://127.0.0.1:8080}
      - LOG_LEVEL=info
      - DEBUG_MODE=false
      - TRUST_PROXY=true
      - JWT_SECRET=${JWT_SECRET:-xitools-production-secret-key-please-change}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - xitools-network

  # XItools前端服务（生产模式）
  frontend:
    image: ghcr.io/justsoow/xitools/frontend:latest
    container_name: xitools-frontend-prod
    restart: always
    depends_on:
      - backend
    networks:
      - xitools-network

  # Nginx反向代理
  nginx:
    image: ghcr.io/justsoow/xitools/nginx:alpine
    container_name: xitools-nginx-prod
    restart: always
    ports:
      - "80:80"     # 直接监听HTTP端口
      - "443:443"   # 直接监听HTTPS端口
    volumes:
      - ./nginx/xitools-docker.conf:/etc/nginx/conf.d/xitools.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro  # 挂载宿主机的SSL证书
      - /var/www/certbot:/var/www/certbot:ro  # Let's Encrypt验证目录
    environment:
      - NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx/conf.d
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "sh", "-c", "nginx -t && curl -f -k https://localhost/health"]
      interval: 30s
      timeout: 20s
      retries: 5
      start_period: 90s
    networks:
      - xitools-network

networks:
  xitools-network:
    driver: bridge

volumes:
  pg_data_prod:

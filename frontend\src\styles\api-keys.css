/**
 * API密钥管理相关样式 - 重构版本
 * 按照设计方案实现的现代化API密钥管理界面样式
 */

/* ================================
   主容器样式
   ================================ */

/* API密钥管理主容器 */
.api-key-management-redesigned {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: 100%;
}

/* 页面头部 */
.api-key-header {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1.5rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.75rem 0;
}

.header-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
  max-width: 600px;
}

/* 创建密钥按钮 */
.create-key-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgb(var(--color-primary));
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(var(--color-primary), 0.2);
}

.create-key-button:hover {
  background: rgb(var(--color-primary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary), 0.3);
}

.create-key-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(var(--color-primary), 0.2);
}

/* API密钥内容区域 */
.api-key-content {
  flex: 1;
  overflow: hidden;
}

/* ================================
   重构后的API密钥列表样式
   ================================ */

.redesigned-api-keys-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* 空状态样式 */
.empty-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
  background: var(--surface);
  border-radius: 12px;
  border: 2px dashed var(--border-color);
  gap: 1.5rem;
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.empty-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  max-width: 400px;
}

.empty-action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgb(var(--color-primary));
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(var(--color-primary), 0.2);
}

.empty-action-button:hover {
  background: rgb(var(--color-primary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary), 0.3);
}

/* ================================
   API密钥卡片样式
   ================================ */

/* 卡片网格布局 */
.api-keys-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  overflow-y: auto;
  padding-right: 0.5rem;
}

/* 单个API密钥卡片 */
.api-key-card {
  background: var(--surface);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.api-key-card:hover {
  border-color: var(--primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 卡片区块 */
.card-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 密钥名称 */
.key-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 密钥前缀显示 */
.key-prefix-container {
  padding: 0.75rem;
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.key-prefix {
  font-family:
    'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--text-primary);
  background: none;
  border: none;
  padding: 0;
  word-break: break-all;
}

/* 权限标签容器 */
.permissions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.permission-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 使用信息 */
.usage-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.usage-time {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

.usage-ip {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* 创建和过期日期 */
.creation-date,
.expiry-date {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

/* 操作按钮 */
.card-actions {
  display: flex;
  gap: 0.75rem;
  padding-top: 0.5rem;
  border-top: 1px solid var(--border-color);
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid rgb(var(--color-text-secondary) / 0.3);
  border-radius: 6px;
  background: rgb(var(--color-surface));
  color: rgb(var(--color-text-secondary));
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
}

.action-button:hover {
  border-color: rgb(var(--color-text-secondary) / 0.5);
  background: rgb(var(--color-text-secondary) / 0.05);
  color: rgb(var(--color-text-primary));
}

.edit-button:hover {
  border-color: rgb(var(--color-primary));
  color: rgb(var(--color-primary));
  background: rgb(var(--color-primary) / 0.05);
}

.revoke-button:hover {
  border-color: rgb(var(--color-error));
  color: rgb(var(--color-error));
  background: rgb(var(--color-error) / 0.05);
}

/* ================================
   撤销API密钥模态框样式
   ================================ */

.revoke-modal-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 警告区域 */
.warning-section {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.warning-icon {
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-message {
  font-size: 0.875rem;
  color: #dc2626;
  font-weight: 500;
  margin: 0 0 0.5rem 0;
}

.warning-description {
  font-size: 0.75rem;
  color: #7f1d1d;
  margin: 0;
  line-height: 1.4;
}

/* 密钥信息区域 */
.key-info-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--background-secondary);
  border-radius: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.info-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.info-value {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

/* 确认输入区域 */
.confirm-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.confirm-label {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

.confirm-input {
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.875rem;
  background: var(--surface);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.confirm-input:focus {
  outline: none;
  border-color: var(--primary);
}

.confirm-input:disabled {
  background: var(--background-secondary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.input-hint {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* 模态框操作按钮 */
.modal-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.cancel-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid rgb(var(--color-text-secondary) / 0.3);
  border-radius: 6px;
  background: rgb(var(--color-surface));
  color: rgb(var(--color-text-secondary));
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover:not(:disabled) {
  border-color: rgb(var(--color-text-secondary) / 0.5);
  background: rgb(var(--color-text-secondary) / 0.05);
  color: rgb(var(--color-text-primary));
}

.confirm-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  background: rgb(var(--color-error));
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(var(--color-error), 0.2);
}

.confirm-button:hover:not(:disabled) {
  background: rgb(var(--color-error) / 0.9);
  box-shadow: 0 4px 8px rgba(var(--color-error), 0.3);
}

.confirm-button:disabled {
  background: rgb(var(--color-text-secondary) / 0.3);
  cursor: not-allowed;
  box-shadow: none;
}

/* 状态标签 */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.active {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
}

.status-badge.inactive {
  background: rgba(156, 163, 175, 0.1);
  color: #6b7280;
}

.status-badge.expired {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* 卡片操作 */
.card-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-ghost.btn-sm {
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid transparent;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-ghost.btn-sm:hover {
  background: var(--background-hover);
  color: var(--text-primary);
}

.btn-ghost.btn-sm.danger:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.2);
}

/* 卡片内容 */
.card-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.info-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.info-item span {
  font-size: 0.875rem;
  color: var(--text-primary);
}

/* 权限标签 */
.permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.permission-tag {
  padding: 0.25rem 0.5rem;
  background: var(--background-secondary);
  color: var(--text-primary);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 卡片详情展开区域 */
.card-details {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.details-divider {
  height: 1px;
  background: var(--border-color);
  margin: 0 0 1rem 0;
}

.usage-stats h5 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-value.success {
  color: #059669;
}

.stat-value.error {
  color: #dc2626;
}

.loading-stats {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* 创建API密钥模态框样式 */
.create-api-key-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-label.required::after {
  content: ' *';
  color: #dc2626;
}

.form-input,
.form-select {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.875rem;
  background: var(--surface);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(var(--accent-rgb), 0.1);
}

.form-help {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

/* 权限选择网格 */
.permissions-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.permission-option {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.permission-option:hover {
  border-color: var(--border-hover);
  background: var(--background-hover);
}

.permission-option input[type='checkbox'] {
  margin: 0.125rem 0 0 0;
}

.permission-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.permission-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.permission-desc {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* 高级选项 */
.advanced-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.advanced-toggle:hover {
  color: var(--text-primary);
}

.advanced-options {
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

/* 安全提示 */
.security-notice {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 8px;
}

.notice-icon {
  color: #3b82f6;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.notice-content h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
}

.notice-content ul {
  margin: 0;
  padding-left: 1.25rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.notice-content li {
  margin-bottom: 0.25rem;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid rgb(var(--color-text-secondary) / 0.2);
}

/* 表单按钮样式 */
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border: 1px solid rgb(var(--color-text-secondary) / 0.3);
  border-radius: 6px;
  background: rgb(var(--color-surface));
  color: rgb(var(--color-text-secondary));
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover:not(:disabled) {
  border-color: rgb(var(--color-text-secondary) / 0.5);
  background: rgb(var(--color-text-secondary) / 0.05);
  color: rgb(var(--color-text-primary));
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  background: rgb(var(--color-primary));
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(var(--color-primary), 0.2);
}

.btn-primary:hover:not(:disabled) {
  background: rgb(var(--color-primary) / 0.9);
  box-shadow: 0 4px 8px rgba(var(--color-primary), 0.3);
}

.btn-primary:disabled {
  background: rgb(var(--color-text-secondary) / 0.3);
  cursor: not-allowed;
  box-shadow: none;
}

/* API密钥成功显示模态框 */
.api-key-success-modal .modal-content {
  max-width: 800px;
}

.success-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.success-header {
  text-align: center;
  padding: 1rem 0;
}

.success-icon {
  font-size: 3rem;
  color: #059669;
  margin-bottom: 1rem;
}

.success-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.success-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* API密钥显示区域 */
.api-key-display {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.key-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.section-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.key-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.api-key-value {
  flex: 1;
  font-family:
    'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--text-primary);
  word-break: break-all;
  background: none;
  border: none;
  padding: 0;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgb(var(--color-accent));
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(var(--color-accent), 0.2);
}

.copy-btn:hover {
  background: rgb(var(--color-accent) / 0.9);
  box-shadow: 0 4px 8px rgba(var(--color-accent), 0.3);
}

.copy-btn.copied {
  background: rgb(var(--color-success));
  box-shadow: 0 2px 4px rgba(var(--color-success), 0.2);
}

/* 安全警告 */
.security-warning {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.15);
  border-radius: 8px;
}

.security-warning i {
  color: #f59e0b;
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.warning-content {
  font-size: 0.875rem;
  color: var(--text-primary);
  line-height: 1.5;
}

/* 使用示例区域 */
.usage-examples {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.examples-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.examples-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.example-tabs {
  display: flex;
  gap: 0.5rem;
}

.tab-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgb(var(--color-text-secondary) / 0.3);
  border-radius: 6px;
  font-size: 0.875rem;
  color: rgb(var(--color-text-secondary));
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  color: rgb(var(--color-text-primary));
  border-color: rgb(var(--color-text-secondary) / 0.5);
  background: rgb(var(--color-text-secondary) / 0.05);
}

.tab-btn.active {
  background: rgb(var(--color-accent));
  border-color: rgb(var(--color-accent));
  color: white;
  box-shadow: 0 2px 4px rgba(var(--color-accent), 0.2);
}

.example-content {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.code-language {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.copy-code-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: transparent;
  border: 1px solid rgb(var(--color-text-secondary) / 0.3);
  border-radius: 4px;
  font-size: 0.75rem;
  color: rgb(var(--color-text-secondary));
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-code-btn:hover {
  color: rgb(var(--color-text-primary));
  border-color: rgb(var(--color-text-secondary) / 0.5);
  background: rgb(var(--color-text-secondary) / 0.05);
}

.code-block {
  margin: 0;
  padding: 1rem;
  background: var(--surface);
  font-family:
    'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.75rem;
  line-height: 1.6;
  overflow-x: auto;
  color: var(--text-primary);
}

/* 快速入门 */
.quick-start h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.steps-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.step-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.step-content h5 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
}

.step-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.5;
}

/* 相关链接 */
.helpful-links h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.links-grid {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

.link-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--surface);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  text-decoration: none;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.link-item:hover {
  border-color: var(--border-hover);
  background: var(--background-hover);
  text-decoration: none;
  color: var(--text-primary);
}

.link-item i {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* 模态框操作 */
.modal-actions {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.btn-lg {
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  background: rgb(var(--color-primary));
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(var(--color-primary), 0.2);
}

.btn-lg:hover {
  background: rgb(var(--color-primary) / 0.9);
  box-shadow: 0 4px 8px rgba(var(--color-primary), 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .keys-grid {
    grid-template-columns: 1fr;
  }

  .list-header {
    flex-direction: column;
    align-items: stretch;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .steps-grid {
    grid-template-columns: 1fr;
  }

  .links-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .examples-header {
    flex-direction: column;
    align-items: stretch;
  }

  .example-tabs {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .key-container {
    flex-direction: column;
    align-items: stretch;
  }

  .copy-btn {
    justify-content: center;
  }

  .api-key-value {
    font-size: 0.75rem;
  }

  .code-block {
    font-size: 0.6rem;
  }
}

# XItools 预生产环境部署工作流
# 简化版预生产部署，专注于快速部署和基础验证

name: 预生产环境部署

on:
  pull_request:
    branches: [ develop ]
    types: [ closed ]
  workflow_dispatch:
    inputs:
      deploy_branch:
        description: '部署分支'
        required: true
        default: 'develop'
        type: string

# 添加必要的权限以支持GitHub Container Registry
permissions:
  contents: read
  packages: write
  id-token: write

env:
  NODE_VERSION: '20.x'
  STAGING_PORT: 8081

jobs:
  # 预生产部署
  deploy-staging:
    name: 部署到预生产环境
    runs-on: ubuntu-latest
    environment: staging
    # 只在PR合并到develop分支时触发，禁止直接push部署
    if: github.event_name == 'pull_request' && github.event.pull_request.merged == true

    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.deploy_branch || 'develop' }}

    - name: 生成版本号
      id: version
      run: |
        VERSION=staging-$(date +%Y%m%d-%H%M%S)-$(git rev-parse --short HEAD)
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "📦 版本号: $VERSION"

    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          backend/package-lock.json

    - name: 快速构建验证
      run: |
        echo "🏗️ 快速构建验证..."

        # 在构建前端之前设置环境变量
        echo "⚙️ 设置前端构建环境变量..."
        cd frontend
        cat > .env.staging << EOF
        VITE_BACKEND_URL=${{ secrets.VITE_BACKEND_URL_STAGING }}
        VITE_NODE_ENV=staging
        EOF

        echo "📋 验证环境变量设置:"
        echo "VITE_BACKEND_URL=${{ secrets.VITE_BACKEND_URL_STAGING }}"

        npm ci
        echo "📝 复制Web版本TypeScript配置..."
        cp tsconfig.web.json tsconfig.json
        echo "🔨 构建前端（Web版本）..."
        npx vite build
        cd ../backend && npm ci && npm run build
        echo "✅ 构建验证完成"


    - name: 构建并推送镜像
      run: |
        echo "🐳 构建 Docker 镜像..."

        # 登录到GitHub Container Registry
        echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

        # 将仓库名称转换为小写以符合GitHub Container Registry要求
        REPO_LOWERCASE=$(echo "${{ github.repository }}" | tr '[:upper:]' '[:lower:]')
        echo "📦 使用镜像仓库: ghcr.io/${REPO_LOWERCASE}"

        # 构建前端镜像
        echo "🔨 构建前端镜像..."
        docker build -t ghcr.io/${REPO_LOWERCASE}/frontend:staging ./frontend --target production

        # 构建后端镜像
        echo "🔨 构建后端镜像..."
        docker build -t ghcr.io/${REPO_LOWERCASE}/backend:staging ./backend --target production

        # 推送前端镜像
        echo "📤 推送前端镜像..."
        docker push ghcr.io/${REPO_LOWERCASE}/frontend:staging

        # 推送后端镜像
        echo "📤 推送后端镜像..."
        docker push ghcr.io/${REPO_LOWERCASE}/backend:staging

        echo "✅ 镜像构建和推送完成"

    - name: 设置 SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: 部署到服务器
      run: |
        echo "🚀 开始部署到预生产环境..."

        # 创建部署目录
        ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
        mkdir -p /opt/xitools-staging/releases/${{ steps.version.outputs.version }}
        mkdir -p /opt/xitools-staging/shared
        EOF

        # 准备配置文件
        cp docker-compose.prod.yml docker-compose.staging.yml
        sed -i 's/8080:80/8081:80/g' docker-compose.staging.yml
        sed -i 's/5432:5432/5433:5432/g' docker-compose.staging.yml
        # 确保nginx配置文件挂载路径正确（已经在生产配置中修复）

        # 创建环境变量文件
        cat > .env.staging << EOF
        NODE_ENV=staging
        DATABASE_URL=postgresql://${{ secrets.POSTGRES_USER }}:${{ secrets.POSTGRES_PASSWORD }}@postgres:5432/xitools_staging
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        POSTGRES_USER=${{ secrets.POSTGRES_USER }}
        POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
        POSTGRES_DB=xitools_staging
        VITE_BACKEND_URL=http://xitools.furdow.com:8081/api
        CORS_ORIGINS=http://xitools.furdow.com:8081
        EOF

        # 上传文件
        scp -o StrictHostKeyChecking=no docker-compose.staging.yml ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/xitools-staging/releases/${{ steps.version.outputs.version }}/docker-compose.prod.yml
        scp -o StrictHostKeyChecking=no .env.staging ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/xitools-staging/releases/${{ steps.version.outputs.version }}/.env.production
        scp -r -o StrictHostKeyChecking=no nginx/ ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/xitools-staging/releases/${{ steps.version.outputs.version }}/

        # 执行部署
        ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
        cd /opt/xitools-staging

        # 彻底清理预发布环境
        echo "🧹 清理预发布环境..."

        # 停止并删除所有XItools相关容器（包括staging）
        echo "🛑 停止XItools容器..."
        docker stop $(docker ps -q --filter "name=xitools") 2>/dev/null || true
        docker rm $(docker ps -aq --filter "name=xitools") 2>/dev/null || true

        # 停止当前服务
        if [ -f current/docker-compose.prod.yml ]; then
          docker-compose -f current/docker-compose.prod.yml down --remove-orphans || true
        fi

        # 清理Docker系统缓存和资源
        echo "🗑️ 清理Docker系统缓存..."
        docker system prune -f

        # 清理XItools相关的Docker网络
        echo "🌐 清理XItools网络..."
        docker network ls --filter "name=xitools" -q | xargs -r docker network rm 2>/dev/null || true

        # 清理XItools相关的Docker卷
        echo "💾 清理XItools数据卷...（跳过以保护数据库）"

        echo "✅ 预发布环境清理完成"

        # 切换到新版本
        ln -sfn releases/${{ steps.version.outputs.version }} current
        cd current

        # 启动服务（强制重新创建）
        echo "🚀 启动预发布环境服务..."
        docker-compose -f docker-compose.prod.yml pull
        docker-compose -f docker-compose.prod.yml up -d --force-recreate --remove-orphans

        echo "✅ 预生产环境部署完成"
        EOF


    - name: 基础健康检查
      run: |
        echo "🏥 等待服务启动并进行健康检查..."
        sleep 30

        # 简单的健康检查
        for i in {1..10}; do
          if curl -f -s --connect-timeout 5 "http://xitools.furdow.com:${{ env.STAGING_PORT }}/" > /dev/null; then
            echo "✅ 预生产环境健康检查通过"
            break
          else
            echo "⏳ 等待服务启动... ($i/10)"
            if [ $i -eq 10 ]; then
              echo "⚠️ 健康检查超时，但部署已完成"
            fi
            sleep 10
          fi
        done

    - name: 部署完成通知
      if: always()
      run: |
        echo "📢 预生产环境部署完成"
        echo "版本: ${{ steps.version.outputs.version }}"
        echo "分支: ${{ github.event.inputs.deploy_branch || 'develop' }}"
        echo "访问地址: http://xitools.furdow.com:${{ env.STAGING_PORT }}"
        echo "部署时间: $(date)"

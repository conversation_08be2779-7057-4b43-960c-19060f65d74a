# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# TypeScript
dist/
*.tsbuildinfo

# 环境变量
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# 日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 测试
coverage/
.nyc_output/

# Prisma
/prisma/migrations/
/prisma/.env
# Prisma生成的客户端代码（应该在每个环境中重新生成）
/src/generated/

# 生成的文件
build/

# IDE和编辑器文件
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

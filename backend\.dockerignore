# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist
build
.next
out

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# 依赖锁定文件（保留package-lock.json用于npm ci）
# package-lock.json  # 注释掉，Docker需要这个文件
yarn.lock

# IDE和编辑器文件
.vscode
.idea
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git相关
.git
.gitignore
README.md

# 测试相关
test
tests
*.test.js
*.spec.js

# 文档
docs
*.md

# 临时文件
tmp
temp
.tmp
.temp

# 开发工具配置
.eslintrc*
.prettierrc*
.editorconfig
# tsconfig.json  # 注释掉，Docker构建需要这个文件
jest.config.js

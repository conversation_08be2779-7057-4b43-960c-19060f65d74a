# XItools Prometheus 配置文件
# 用于监控应用性能和系统指标

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'xitools-production'
    environment: 'production'

# 告警规则文件
rule_files:
  - "alert-rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 数据抓取配置
scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # XItools 后端应用监控
  - job_name: 'xitools-backend'
    static_configs:
      - targets: ['backend:3000']
    scrape_interval: 15s
    metrics_path: '/metrics'
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # XItools 前端应用监控
  - job_name: 'xitools-frontend'
    static_configs:
      - targets: ['frontend:5173']
    scrape_interval: 30s
    metrics_path: '/metrics'
    scrape_timeout: 10s

  # PostgreSQL 数据库监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # Nginx 监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # Node Exporter (系统监控)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: '/metrics'

  # Docker 容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # 黑盒监控 (外部可用性检查)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://xitools.furdow.com
        - https://xitools.furdow.com/api/health
        - https://xitools.furdow.com/api/mcp/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # SSL 证书监控
  - job_name: 'ssl-exporter'
    static_configs:
      - targets: ['ssl-exporter:9219']
    scrape_interval: 300s  # 5分钟检查一次
    params:
      target: ['xitools.furdow.com:443']

# 远程写入配置 (可选，用于长期存储)
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置 (可选)
# remote_read:
#   - url: "http://remote-storage:9201/read"

# 存储配置
storage:
  tsdb:
    path: /prometheus/data
    retention.time: 30d
    retention.size: 10GB
    wal-compression: true

# Web 配置
web:
  console.templates: /etc/prometheus/consoles
  console.libraries: /etc/prometheus/console_libraries
  enable-lifecycle: true
  enable-admin-api: true
  max-connections: 512
  read-timeout: 30s

# 日志配置
log:
  level: info
  format: logfmt

/* 导入认证相关样式 */
@import './styles/auth.css';

/* 导入加载状态样式 */
@import './styles/loading.css';

/* 导入用户体验增强样式 */
@import './styles/user-experience.css';

/* 导入API密钥管理样式 */
@import './styles/api-keys.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 浅色主题 (Light Theme) 基础变量 */
    --color-primary: 79 70 229; /* #4F46E5 */
    --color-secondary: 16 185 129; /* #10B981 */
    --color-accent: 245 158 11; /* #F59E0B */
    --color-background: 255 255 255; /* #FFFFFF */
    --color-surface: 243 244 246; /* #F3F4F6 */
    --color-text-primary: 17 24 39; /* #111827 */
    --color-text-secondary: 107 114 128; /* #6B7280 */
    --color-success: 16 185 129; /* #10B981 */
    --color-warning: 251 191 36; /* #FBBF24 */
    --color-error: 239 68 68; /* #EF4444 */

    /* 卡片毛玻璃效果 */
    --card-blur: 10px;
    --card-bg-opacity-light: 0.2;
    --card-bg-opacity-dark: 0.3;

    /* 卡片背景色变量 */
    --card-bg-default: 255 255 255; /* #FFFFFF */
    --card-bg-gray: 243 244 246; /* #F3F4F6 */
    --card-bg-blue: 219 234 254; /* #DBEAFE */
    --card-bg-green: 220 252 231; /* #DCFCE7 */
    --card-bg-purple: 243 232 255; /* #F3E8FF */
    --card-bg-yellow: 254 249 195; /* #FEF9C3 */
    --card-bg-red: 254 226 226; /* #FEE2E2 */
    --card-bg-orange: 255 237 213; /* #FFEDD5 */
    --card-bg-cyan: 207 250 254; /* #CFFAFE */
    --card-bg-pink: 252 231 243; /* #FCE7F3 */

    /* 中等饱和度色系 */
    --card-bg-sky: 186 230 253; /* #BAE6FD */
    --card-bg-grass: 187 247 208; /* #BBF7D0 */
    --card-bg-lavender: 221 214 254; /* #DDD6FE */
    --card-bg-lemon: 254 240 138; /* #FEF08A */
    --card-bg-coral: 254 202 202; /* #FECACA */
    --card-bg-apricot: 254 215 170; /* #FED7AA */
    --card-bg-aqua: 153 246 228; /* #99F6E4 */
    --card-bg-rose: 251 207 232; /* #FBCFE8 */

    /* 看板背景色变量 - 独立于主题 */
    --board-background-color: #ffffff;

    /* 通用 UI 变量 */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary-foreground: 210 40% 98%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: var(--color-primary);
    --radius: 0.5rem;
  }

  .dark {
    /* 深色主题 (Dark Theme) 基础变量 */
    --color-primary: 99 102 241; /* #6366F1 */
    --color-secondary: 52 211 153; /* #34D399 */
    --color-accent: 251 191 36; /* #FBBF24 */
    --color-background: 31 41 55; /* #1F2937 */
    --color-surface: 55 65 81; /* #374151 */
    --color-text-primary: 249 250 251; /* #F9FAFB */
    --color-text-secondary: 209 213 219; /* #D1D5DB */
    --color-success: 52 211 153; /* #34D399 */
    --color-warning: 251 191 36; /* #FBBF24 */
    --color-error: 248 113 113; /* #F87171 */

    /* 深色主题卡片背景色变量 */
    --card-bg-default: 55 65 81; /* #374151 */
    --card-bg-gray: 75 85 99; /* #4B5563 */
    --card-bg-blue: 30 64 175; /* #1E40AF */
    --card-bg-green: 22 101 52; /* #166534 */
    --card-bg-purple: 107 33 168; /* #6B21A8 */
    --card-bg-yellow: 180 83 9; /* #B45309 */
    --card-bg-red: 153 27 27; /* #991B1B */
    --card-bg-orange: 180 83 9; /* #B45309 */
    --card-bg-cyan: 17 94 89; /* #115E59 */
    --card-bg-pink: 157 23 77; /* #9D174D */

    /* 通用 UI 变量 */
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary-foreground: 210 40% 98%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
  }

  /* 樱花主题 (Cherry Blossom Theme) */
  .theme-cherry {
    --color-primary: 233 30 99; /* #E91E63 */
    --color-secondary: 255 152 0; /* #FF9800 */
    --color-accent: 156 39 176; /* #9C27B0 */
    --color-background: 255 248 245; /* #FFF8F5 */
    --color-surface: 252 228 236; /* #FCE4EC */
    --color-text-primary: 74 20 140; /* #4A148C */
    --color-text-secondary: 136 14 79; /* #880E4F */
    --color-success: 76 175 80; /* #4CAF50 */
    --color-warning: 255 193 7; /* #FFC107 */
    --color-error: 244 67 54; /* #F44336 */

    /* 樱花主题卡片背景色变量 */
    --card-bg-default: 255 248 245; /* #FFF8F5 */
    --card-bg-gray: 252 228 236; /* #FCE4EC */
    --card-bg-blue: 227 242 253; /* #E3F2FD */
    --card-bg-green: 232 245 233; /* #E8F5E8 */
    --card-bg-purple: 243 229 245; /* #F3E5F5 */
    --card-bg-yellow: 255 248 225; /* #FFF8E1 */
    --card-bg-red: 255 235 238; /* #FFEBEE */
    --card-bg-orange: 255 243 224; /* #FFF3E0 */
    --card-bg-cyan: 224 247 250; /* #E0F7FA */
    --card-bg-pink: 252 228 236; /* #FCE4EC */
  }

  /* 海洋主题 (Ocean Theme) */
  .theme-ocean {
    --color-primary: 0 188 212; /* #00BCD4 */
    --color-secondary: 76 175 80; /* #4CAF50 */
    --color-accent: 255 87 34; /* #FF5722 */
    --color-background: 240 248 255; /* #F0F8FF */
    --color-surface: 224 242 241; /* #E0F2F1 */
    --color-text-primary: 1 87 155; /* #01579B */
    --color-text-secondary: 0 77 64; /* #004D40 */
    --color-success: 46 125 50; /* #2E7D32 */
    --color-warning: 255 143 0; /* #FF8F00 */
    --color-error: 211 47 47; /* #D32F2F */

    /* 海洋主题卡片背景色变量 */
    --card-bg-default: 240 248 255; /* #F0F8FF */
    --card-bg-gray: 224 242 241; /* #E0F2F1 */
    --card-bg-blue: 225 245 254; /* #E1F5FE */
    --card-bg-green: 232 245 233; /* #E8F5E8 */
    --card-bg-purple: 237 231 246; /* #EDE7F6 */
    --card-bg-yellow: 255 248 225; /* #FFF8E1 */
    --card-bg-red: 255 235 238; /* #FFEBEE */
    --card-bg-orange: 255 243 224; /* #FFF3E0 */
    --card-bg-cyan: 224 247 250; /* #E0F7FA */
    --card-bg-pink: 248 187 208; /* #F8BBD0 */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-text-primary;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

@layer components {
  /* 圆角设计系统 */
  .rounded-container {
    @apply rounded-2xl; /* 16px - 主要容器 */
  }

  .rounded-card {
    @apply rounded-xl; /* 12px - 卡片容器 */
  }

  .rounded-element {
    @apply rounded-lg; /* 8px - 普通元素 */
  }

  .rounded-small {
    @apply rounded; /* 4px - 小元素 */
  }

  /* 看板背景色样式 */
  .board-background {
    background: var(--board-background-color);
  }

  /* 看板内容区域样式 */
  .board-content {
    background: var(--board-background-color);
    min-height: 100%;
  }

  /* 现代卡片式布局 */
  .modern-container {
    @apply rounded-container bg-surface shadow-sm border border-border/50;
  }

  /* 看板容器 - 使用自定义背景色，优先级更高 */
  .modern-container.board-content {
    background: var(--board-background-color) !important;
    border: 2px solid rgba(var(--color-border), 0.2) !important;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  }

  /* 深色主题下的看板容器增强 */
  .dark .modern-container.board-content {
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.3),
      0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  }

  /* 自定义滚动条样式 */
  /* Webkit浏览器滚动条 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #a0a0a0; /* 浅色主题使用中等灰色，更好搭配 */
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #808080; /* 悬停时稍深一点 */
  }

  /* 深色主题下的滚动条 */
  .dark ::-webkit-scrollbar-track {
    background: transparent;
  }

  .dark ::-webkit-scrollbar-thumb {
    background: #bbbbbb; /* 深色主题使用浅灰色 */
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: #dddddd; /* 悬停时更浅 */
  }

  /* 深色主题下的自定义滚动条 */
  .dark .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent; /* 删除背景 */
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #bbbbbb; /* 深色主题使用浅灰色 */
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #dddddd; /* 悬停时更浅 */
  }

  /* 樱花主题下的滚动条 */
  .theme-cherry ::-webkit-scrollbar-thumb {
    background: rgba(233, 30, 99, 0.3);
  }

  .theme-cherry ::-webkit-scrollbar-thumb:hover {
    background: rgba(233, 30, 99, 0.5);
  }

  /* 樱花主题下的自定义滚动条 */
  .theme-cherry .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(233, 30, 99, 0.4);
    border: 1px solid rgba(233, 30, 99, 0.2);
  }

  .theme-cherry .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(233, 30, 99, 0.6);
  }

  /* 海洋主题下的滚动条 */
  .theme-ocean ::-webkit-scrollbar-thumb {
    background: rgba(0, 188, 212, 0.3);
  }

  .theme-ocean ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 188, 212, 0.5);
  }

  /* 海洋主题下的自定义滚动条 */
  .theme-ocean .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 188, 212, 0.4);
    border: 1px solid rgba(0, 188, 212, 0.2);
  }

  .theme-ocean .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 188, 212, 0.6);
  }

  /* Firefox滚动条样式 */
  * {
    scrollbar-width: thin;
    scrollbar-color: #a0a0a0 transparent; /* 浅色主题使用中等灰色 */
  }

  .dark * {
    scrollbar-color: #bbbbbb transparent; /* 深色主题使用浅灰色 */
  }

  .theme-cherry * {
    scrollbar-color: rgba(233, 30, 99, 0.6) transparent;
  }

  .theme-ocean * {
    scrollbar-color: rgba(0, 188, 212, 0.6) transparent;
  }

  /* 自定义滚动条类 - 确保滚动条可见 */
  .custom-scrollbar {
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #888888 rgba(0, 0, 0, 0.1);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 12px; /* 增加宽度使其更明显 */
    height: 12px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05); /* 添加轻微背景 */
    border-radius: 6px;
    margin: 2px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #888888; /* 使用更深的灰色 */
    border-radius: 6px;
    border: 2px solid transparent;
    background-clip: content-box;
    transition: all 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #666666; /* 悬停时更深 */
    background-clip: content-box;
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 深色主题下的自定义滚动条 */
  .dark .custom-scrollbar {
    scrollbar-color: #bbbbbb transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #bbbbbb;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #dddddd;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 看板内容区域的滚动条样式 */
  .board-content ::-webkit-scrollbar {
    width: 6px;
    height: 8px; /* 水平滚动条稍微明显一点 */
  }

  .board-content ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03); /* 浅色主题轻微背景 */
    border-radius: 4px;
    margin: 4px;
  }

  .board-content ::-webkit-scrollbar-thumb {
    background: #a0a0a0; /* 浅色主题使用中等灰色 */
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .board-content ::-webkit-scrollbar-thumb:hover {
    background: #808080; /* 悬停时稍深 */
  }

  /* 深色主题下的看板滚动条 */
  .dark .board-content ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.03);
  }

  .dark .board-content ::-webkit-scrollbar-thumb {
    background: #bbbbbb;
  }

  .dark .board-content ::-webkit-scrollbar-thumb:hover {
    background: #dddddd;
  }

  /* 列内部滚动条样式 - 隐藏滚动条但保持滚动功能 */
  .board-content .overflow-y-auto::-webkit-scrollbar {
    width: 0px; /* 隐藏滚动条 */
    background: transparent;
  }

  .board-content .overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
  }

  .board-content .overflow-y-auto::-webkit-scrollbar-thumb {
    background: transparent;
  }

  /* Firefox下隐藏列内滚动条 */
  .board-content .overflow-y-auto {
    scrollbar-width: none; /* Firefox */
  }

  .modern-card {
    @apply rounded-card bg-background shadow-sm border border-border/30;
  }

  /* 侧边栏样式 */
  .sidebar-container {
    @apply rounded-container bg-surface/80 backdrop-blur-sm border border-border/50 shadow-lg;
  }

  /* 菜单项样式 */
  .menu-item {
    @apply rounded-element transition-all duration-200 hover:bg-primary/10 hover:shadow-sm;
  }

  .menu-item-active {
    @apply bg-primary/15 shadow-sm border border-primary/20;
  }
}

@layer components {
  .card {
    @apply bg-white bg-opacity-20 backdrop-blur-md border border-white/30 rounded-lg shadow-lg;
  }
  .dark .card {
    @apply bg-dark-surface bg-opacity-30 border-gray-100/10;
  }

  .btn-primary {
    @apply bg-primary text-white rounded-md py-2 px-4 hover:bg-primary/90 transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary text-white rounded-md py-2 px-4 hover:bg-secondary/90 transition-colors;
  }

  .tag {
    @apply bg-accent bg-opacity-20 text-accent px-2 py-1 rounded;
  }

  /* 拖拽相关样式 */
  .dragging {
    @apply opacity-50 transform rotate-3 scale-105;
  }

  .drag-overlay {
    @apply transform rotate-3 scale-105 shadow-2xl opacity-95;
    z-index: 9999;
  }

  .drop-zone-active {
    @apply ring-2 ring-primary ring-opacity-50 bg-primary/5 scale-105;
  }

  /* 确保下拉菜单始终在最顶层 */
  .dropdown-menu {
    z-index: 9999 !important;
    position: relative;
  }

  /* 用户菜单样式 */
  .user-menu {
    position: relative;
  }

  .user-menu-trigger {
    @apply flex items-center w-full px-3 py-2 text-left bg-transparent border-0 rounded-lg transition-all duration-200;
    @apply hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/20;
  }

  .user-menu-trigger.collapsed {
    @apply justify-center px-2;
  }

  .user-menu-trigger.expanded {
    @apply justify-between;
  }

  .user-menu-trigger.active {
    @apply bg-gray-100 dark:bg-gray-700;
  }

  .user-avatar {
    @apply relative flex-shrink-0;
  }

  .user-avatar .avatar-image {
    @apply w-8 h-8 rounded-full object-cover;
  }

  .user-avatar .avatar-placeholder {
    @apply w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400;
  }

  .user-avatar.large .avatar-image {
    @apply w-12 h-12;
  }

  .user-avatar.large .avatar-placeholder {
    @apply w-12 h-12;
  }

  .status-indicator {
    @apply absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white;
  }

  .status-indicator.online {
    @apply bg-green-500;
  }

  .user-info {
    @apply flex-1 min-w-0 ml-3;
  }

  .user-name {
    @apply text-sm font-medium text-gray-900 dark:text-gray-100 truncate;
  }

  .user-email {
    @apply text-xs text-gray-600 dark:text-gray-400 truncate;
  }

  .user-role {
    @apply text-xs text-gray-600 dark:text-gray-400;
  }

  .dropdown-arrow {
    @apply flex-shrink-0 ml-2;
  }

  .dropdown-arrow .icon-chevron-down {
    @apply w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200;
  }

  .dropdown-arrow .icon-chevron-down.rotated {
    @apply transform rotate-180;
  }

  .user-menu-dropdown {
    @apply absolute bottom-full left-0 mb-2 w-56 rounded-lg shadow-lg border z-50;
    @apply bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700;
    opacity: 0;
    transform: translateY(8px);
    animation: slideInFromBottom 0.2s ease-out forwards;
  }

  @keyframes slideInFromBottom {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .user-menu-dropdown.collapsed {
    @apply left-auto right-0 w-56;
  }

  .dropdown-content {
    @apply p-2;
  }

  .dropdown-header {
    @apply flex items-center space-x-3 p-3 border-b border-gray-200 dark:border-gray-700;
  }

  .dropdown-menu {
    @apply space-y-1;
  }

  .menu-item {
    @apply flex items-center w-full px-3 py-2 text-sm text-gray-900 dark:text-gray-100 rounded-lg transition-colors duration-200;
    @apply hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/20;
  }

  .menu-item.logout {
    @apply text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20;
  }

  .menu-item svg {
    @apply w-4 h-4 mr-3 flex-shrink-0;
  }

  .menu-divider {
    @apply h-px bg-gray-200 dark:bg-gray-700 my-2;
  }

  .dropdown-footer {
    @apply border-t border-gray-200 dark:border-gray-700 pt-2 mt-2;
  }

  .quick-info {
    @apply space-y-1;
  }

  .info-item {
    @apply flex items-center text-xs text-gray-600 dark:text-gray-400;
  }

  .info-item svg {
    @apply w-3 h-3 mr-2;
  }

  /* 用户资料模态框样式 */
  .user-profile-modal {
    @apply max-w-none;
  }

  .profile-header {
    @apply border-b border-gray-200 dark:border-gray-700 pb-4;
  }

  /* 任务卡片更多按钮容器 */
  .task-more-button {
    z-index: 100;
    position: relative;
  }

  .sortable-ghost {
    @apply opacity-50;
  }

  .sortable-chosen {
    @apply transform rotate-3 scale-105;
  }

  .sortable-drag {
    @apply opacity-0;
  }

  /* 文本截断样式 */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  /* 导航概览动画效果 */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 2s ease-in-out infinite;
  }

  .animate-slide-up {
    animation: slideUp 0.4s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes bounceGentle {
    0%,
    20%,
    50%,
    80%,
    100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* 卡片悬停动画 */
  .card-hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
  }

  .card-hover-lift:hover {
    transform: translateY(-6px);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.12),
      0 8px 16px rgba(0, 0, 0, 0.08);
  }

  /* 深色主题下的卡片悬停效果 */
  .dark .card-hover-lift:hover {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 8px 16px rgba(0, 0, 0, 0.2);
  }

  /* 侧边栏项目悬停动画 */
  .sidebar-item-hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-item-hover:hover {
    transform: translateX(2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  /* 统计卡片特殊动画 */
  .stats-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0) scale(1);
  }

  .stats-card:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  }

  .dark .stats-card:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.25);
  }

  /* 侧边栏项目样式优化 */
  .sidebar-item {
    position: relative;
  }
}

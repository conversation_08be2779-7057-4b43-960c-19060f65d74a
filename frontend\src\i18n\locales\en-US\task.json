{"title": "Task", "titles": "Tasks", "fields": {"id": "Task ID", "title": "Title", "description": "Description", "status": "Status", "priority": "Priority", "assignee": "Assignee", "dueDate": "Due Date", "createdAt": "Created At", "updatedAt": "Updated At", "tags": "Tags", "tag": "Tag", "progress": "Progress", "estimate": "Estimated Time", "actualTime": "Actual Time", "estimatedEffort": "Estimated E<PERSON>ort (hours)", "loggedTime": "Logged Time (hours)", "category": "Category", "project": "Project", "milestone": "Milestone", "dependencies": "Dependencies", "attachments": "Attachments", "comments": "Comments", "history": "History", "sortOrder": "Sort Order", "notSet": "Not Set", "tagCount": "Tag Count"}, "status": {"todo": "To Do", "inProgress": "In Progress", "review": "Review", "testing": "Testing", "done": "Done", "cancelled": "Cancelled", "blocked": "Blocked", "onHold": "On Hold", "current": "Current"}, "priority": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>", "critical": "Critical", "none": "None"}, "actions": {"createTask": "Create Task", "editTask": "Edit Task", "deleteTask": "Delete Task", "duplicateTask": "Duplicate Task", "moveTask": "Move Task", "assignTask": "Assign Task", "completeTask": "Complete Task", "reopenTask": "Reopen Task", "archiveTask": "Archive Task", "restoreTask": "Restore Task", "addComment": "Add Comment", "addAttachment": "Add Attachment", "addTag": "Add Tag", "removeTag": "Remove Tag", "setDueDate": "Set Due Date", "removeDueDate": "Remove Due Date", "setPriority": "Set Priority", "setAssignee": "<PERSON>ee", "updateProgress": "Update Progress", "addDependency": "Add Dependency", "removeDependency": "Remove Dependency", "deleteAllTasks": "Delete All Tasks", "deleteAll": "Delete All", "setColor": "Set Color", "setTaskColor": "Set Task Color", "delete": "Delete Task", "duplicate": "Duplicate Task", "copyLink": "Copy Link", "statusChange": "Status Change", "priorityChange": "Priority Setting", "otherActions": "Other Actions", "deleteConfirm": "Are you sure you want to delete this task? This action cannot be undone.", "duplicateNotImplemented": "Duplicate feature not implemented yet", "cancelSelection": "Cancel Selection", "batchDelete": "<PERSON><PERSON> Delete", "createFirst": "Create First Task"}, "placeholders": {"taskTitle": "Enter task title", "title": "Enter task title", "taskDescription": "Enter task description", "description": "Enter task description...", "searchTasks": "Search tasks...", "selectAssignee": "Select assignee", "assignee": "Enter assignee", "selectPriority": "Select priority", "selectStatus": "Select status", "selectDueDate": "Select due date", "dueDate": "Set due date", "addTag": "Add tag", "tags": "Separate multiple tags with commas", "acceptanceCriteria": "Enter acceptance criteria...", "addComment": "Add comment..."}, "messages": {"taskCreated": "Task created successfully", "taskUpdated": "Task updated successfully", "taskDeleted": "Task deleted successfully", "taskCompleted": "Task completed", "taskReopened": "Task reopened", "taskArchived": "Task archived", "taskRestored": "Task restored", "taskMoved": "Task moved", "taskAssigned": "Task assigned", "taskDuplicated": "Task duplicated", "commentAdded": "Comment added", "attachmentAdded": "Attachment added", "tagAdded": "Tag added", "tagRemoved": "Tag removed", "dueDateSet": "Due date set", "dueDateRemoved": "Due date removed", "prioritySet": "Priority set", "assigneeSet": "Assignee set", "progressUpdated": "Progress updated", "dependencyAdded": "Dependency added", "dependencyRemoved": "Dependency removed", "noTasks": "No tasks available", "noTasksFound": "No matching tasks found", "taskTitleRequired": "Task title is required", "taskNotFound": "Task not found", "cannotDeleteTask": "Cannot delete task", "cannotMoveTask": "Cannot move task", "taskOverdue": "Task is overdue", "taskDueSoon": "Task is due soon", "taskBlocked": "Task is blocked", "taskWaiting": "Task is waiting", "confirmDeleteAll": "Are you sure you want to delete all {{count}} tasks? This action cannot be undone.", "allTasksDeleted": "All tasks deleted", "noTasksDescription": "Start creating your first task to make your work more organized. You can set priorities, due dates, and assignees.", "noSearchResults": "No tasks found containing \"{{searchTerm}}\". Try using different keywords or clear the search.", "noFilterResults": "No tasks match the criteria", "noFilterResultsDescription": "No tasks found with current filter conditions. Try adjusting the filters or clear all filters."}, "filters": {"title": "<PERSON><PERSON>", "all": "All", "myTasks": "My Tasks", "unassigned": "Unassigned", "overdue": "Overdue", "dueSoon": "Due Soon", "completed": "Completed", "inProgress": "In Progress", "highPriority": "High Priority", "lowPriority": "Low Priority", "recentlyCreated": "Recently Created", "recentlyUpdated": "Recently Updated", "withAttachments": "With Attachments", "withComments": "With Comments", "withTags": "With Tags", "blocked": "Blocked"}, "sorting": {"title": "By Title", "status": "By Status", "priority": "By Priority", "assignee": "By Assignee", "dueDate": "By Due Date", "createdAt": "By Created Date", "updatedAt": "By Updated Date", "progress": "By Progress", "estimate": "By Estimated Time", "actualTime": "By Actual Time", "ascending": "Ascending", "descending": "Descending"}, "statistics": {"total": "{{count}} task", "total_other": "{{count}} tasks", "completed": "Completed", "inProgress": "In Progress", "overdue": "Overdue", "dueSoon": "Due Soon", "completionRate": "Completion Rate", "averageTime": "Average Time", "totalTime": "Total Time", "estimatedTime": "Estimated Time", "actualTime": "Actual Time", "efficiency": "Efficiency", "showing": "Showing {{displayed}} / {{total}} tasks", "selectedTasks": "Selected {{count}} tasks"}, "detail": {"title": "Task Details", "notFound": "Task not found or failed to load", "tabs": {"details": "Details", "timeline": "Timeline", "actions": "Actions"}, "sections": {"basicInfo": "Basic Information", "description": "Task Description", "acceptanceCriteria": "Acceptance Criteria", "timeTracking": "Time Tracking", "metadata": "<PERSON><PERSON><PERSON>", "taskInfo": "Task Information"}, "timelineDescription": "View all changes made to this task", "actionsDescription": "Quickly modify task status and properties"}, "timeline": {"events": {"created": "Task Created", "updated": "Task Updated", "statusChanged": "Status Changed", "assigned": "Task Assigned", "commented": "Comment Added", "completed": "Task Completed", "unknown": "Unknown Action"}, "time": {"justNow": "Just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago", "unknown": "Unknown time"}, "noHistory": "No operation history", "by": "by", "system": "System", "descriptions": {"created": "Created task \"{{title}}\"", "updated": "Task information updated", "completed": "Task marked as completed"}}, "units": {"hours": " hours", "count": ""}}
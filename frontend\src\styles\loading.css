/*
 * @Author: XItools Team
 * @Date: 2025-07-01 16:45:00
 * @LastEditors: XItools Team
 * @LastEditTime: 2025-07-01 16:45:00
 * @FilePath: \XItools\frontend\src\styles\loading.css
 * @Description: 加载状态指示器样式
 * 
 * Copyright (c) 2025 by XItools Team, All Rights Reserved. 
 */

/* 加载容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  --loading-color: rgb(var(--color-primary));
}

.loading-text {
  font-size: 0.875rem;
  color: rgb(var(--color-text-secondary));
  text-align: center;
  font-weight: 500;
}

/* 加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* 旋转加载器 */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-circle {
  border: 2px solid rgb(var(--color-surface));
  border-top: 2px solid var(--loading-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-small .spinner-circle {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.loading-medium .spinner-circle {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.loading-large .spinner-circle {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

/* 点状加载器 */
.loading-dots {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.loading-dots .dot {
  background: var(--loading-color);
  border-radius: 50%;
  animation: dot-bounce 1.4s ease-in-out infinite both;
}

.loading-small .dot {
  width: 4px;
  height: 4px;
}

.loading-medium .dot {
  width: 6px;
  height: 6px;
}

.loading-large .dot {
  width: 8px;
  height: 8px;
}

.loading-dots .dot:nth-child(1) {
  animation-delay: -0.32s;
}
.loading-dots .dot:nth-child(2) {
  animation-delay: -0.16s;
}
.loading-dots .dot:nth-child(3) {
  animation-delay: 0s;
}

/* 脉冲加载器 */
.loading-pulse {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-circle {
  background: var(--loading-color);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.loading-small .pulse-circle {
  width: 16px;
  height: 16px;
}

.loading-medium .pulse-circle {
  width: 24px;
  height: 24px;
}

.loading-large .pulse-circle {
  width: 32px;
  height: 32px;
}

/* 骨架屏加载器 */
.loading-skeleton,
.skeleton-loader {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.skeleton-loader {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(
    90deg,
    rgb(var(--color-surface)) 25%,
    rgb(var(--color-surface) / 0.5) 50%,
    rgb(var(--color-surface)) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s ease-in-out infinite;
  flex-shrink: 0;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-line {
  height: 12px;
  border-radius: 6px;
  background: linear-gradient(
    90deg,
    rgb(var(--color-surface)) 25%,
    rgb(var(--color-surface) / 0.5) 50%,
    rgb(var(--color-surface)) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s ease-in-out infinite;
}

.skeleton-line.short {
  width: 60%;
}

.loading-small .skeleton-line {
  height: 8px;
}

.loading-medium .skeleton-line {
  height: 12px;
}

.loading-large .skeleton-line {
  height: 16px;
}

/* 内联加载器 */
.inline-loader {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.spinner-inline {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-inline.small .spinner-circle {
  width: 12px;
  height: 12px;
  border-width: 1.5px;
}

.spinner-inline.medium .spinner-circle {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.spinner-inline.large .spinner-circle {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

/* 页面加载器 */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(var(--color-background));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.page-loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.loader-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-logo .logo-image {
  width: 64px;
  height: 64px;
  opacity: 0.8;
  animation: logo-pulse 2s ease-in-out infinite;
}

/* 动画定义 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dot-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes logo-pulse {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* 按钮加载状态 */
.submit-button.loading {
  position: relative;
  pointer-events: none;
}

.submit-button.loading .button-text {
  opacity: 0.7;
}

.submit-button .icon-loader {
  animation: spin 1s linear infinite;
}

/* 表单加载状态 */
.form-loading {
  position: relative;
  pointer-events: none;
}

.form-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  border-radius: inherit;
  z-index: 1;
}

.form-loading .loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-loader-content {
    gap: 1.5rem;
    padding: 2rem;
  }

  .loader-logo .logo-image {
    width: 48px;
    height: 48px;
  }

  .loading-text {
    font-size: 0.8rem;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .loading-overlay {
    background: rgba(0, 0, 0, 0.7);
  }

  .form-loading::after {
    background: rgba(0, 0, 0, 0.8);
  }
}

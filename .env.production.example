# XItools生产环境配置
# 复制命令: cp .env.production.example .env.production

# 服务器配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
TRUST_PROXY=true

# 数据库配置
DATABASE_URL=*********************************************************/xitools

# JWT配置
JWT_SECRET=xitools-prod-jwt-secret-$(date +%s)-secure-key-2024
JWT_EXPIRES_IN=7d

# CORS配置
CORS_ORIGINS=https://xitools.furdow.com,http://xitools.furdow.com,http://localhost:8080

# 日志配置
LOG_LEVEL=info
DEBUG_MODE=false

# PostgreSQL数据库密码
POSTGRES_PASSWORD=xitools_prod_password

# 前端配置
VITE_BACKEND_URL=https://xitools.furdow.com
VITE_API_TIMEOUT=30000
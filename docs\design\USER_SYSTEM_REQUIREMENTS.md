# XItools 用户系统需求分析文档

## 1. 项目背景

XItools是一个智能任务看板应用，目前支持：
- 多级导航系统（工作区→项目→看板）
- MCP服务集成，支持外部LLM交互
- 多视图任务管理（看板、列表、日历）
- 实时同步和多主题支持

**当前状态**：单用户模式，所有数据共享，无用户认证机制

## 2. 用户系统需求概述

### 2.1 核心需求
- **用户注册与登录**：支持邮箱/用户名注册，安全登录机制
- **数据隔离**：每个用户只能访问自己的工作区、项目、看板和任务
- **会话管理**：安全的用户会话管理，支持自动登录和登出
- **权限控制**：基于用户的数据访问权限控制

### 2.2 特殊需求
- **MCP服务兼容**：确保外部LLM通过MCP访问时能正确识别用户身份
- **现有数据处理**：妥善处理当前已存在的无用户关联数据
- **多语言支持**：认证界面支持中英文切换

## 3. 用户认证架构设计

### 3.1 认证方案选择
**推荐方案：JWT + bcrypt**
- JWT用于无状态认证，适合前后端分离架构
- bcrypt用于密码加密，提供强安全性
- 支持token刷新机制，平衡安全性和用户体验

### 3.2 用户注册流程
```
用户填写注册信息 → 后端验证 → 密码加密存储 → 创建默认工作区 → 返回JWT token → 自动登录
```

### 3.3 用户登录流程
```
用户输入凭据 → 后端验证 → 生成JWT token → 前端存储token → 加载用户数据 → 进入主应用
```

### 3.4 会话管理策略
- **Token存储**：localStorage存储JWT token
- **Token过期**：设置合理的过期时间（如7天）
- **自动刷新**：token即将过期时自动刷新
- **安全登出**：清除本地token，可选择性撤销服务端token

## 4. 数据库架构设计

### 4.1 新增用户相关表

#### User表（用户基本信息）
```sql
- id: UUID (主键)
- username: String (唯一，用户名)
- email: String (唯一，邮箱)
- passwordHash: String (加密后的密码)
- displayName: String (显示名称)
- avatar: String? (头像URL，可选)
- isActive: Boolean (账户是否激活)
- lastLoginAt: DateTime? (最后登录时间)
- createdAt: DateTime (创建时间)
- updatedAt: DateTime (更新时间)
```

#### UserSession表（会话管理）
```sql
- id: UUID (主键)
- userId: UUID (外键，关联User)
- tokenHash: String (token哈希值)
- expiresAt: DateTime (过期时间)
- isRevoked: Boolean (是否已撤销)
- userAgent: String? (用户代理信息)
- ipAddress: String? (IP地址)
- createdAt: DateTime (创建时间)
```

### 4.2 现有表结构修改

需要为以下表添加userId字段：
- **Workspace** → 添加 `ownerId: UUID` (关联User.id)
- **Project** → 添加 `ownerId: UUID` (关联User.id)
- **Board** → 添加 `ownerId: UUID` (关联User.id)
- **Task** → 添加 `ownerId: UUID` (关联User.id)
- **BoardColumn** → 通过Board间接关联用户
- **Tag** → 添加 `ownerId: UUID` (关联User.id)

## 5. 权限管理模型

### 5.1 权限级别设计
**简化权限模型**（初期实现）：
- **Owner**：数据所有者，拥有完全控制权
- **数据隔离**：用户只能访问自己创建的数据

### 5.2 权限检查策略
- **API级别**：所有API端点都需要验证用户身份和数据所有权
- **前端级别**：前端只显示用户有权访问的数据
- **MCP级别**：MCP工具操作需要包含用户上下文

## 6. MCP服务用户上下文集成

### 6.1 实施策略
**基于您的MCP用户认证计划文档，MCP服务用户上下文集成将在用户系统基础功能完成后单独实施。**

### 6.2 预定方案概述
- **API Key认证机制**：用户在Web界面生成API密钥，配置到Cursor MCP
- **HTTP认证传递**：npm包通过HTTP请求携带API Key，服务器验证并获取用户身份
- **数据隔离**：所有MCP工具操作基于用户身份进行数据过滤
- **使用日志**：记录MCP工具使用情况和安全审计

### 6.3 当前阶段处理
在用户系统基础功能开发阶段，MCP服务暂时保持现有架构不变，待用户认证系统稳定后再进行集成。

## 7. 现有数据迁移策略

### 7.1 迁移方案确定
**已确定方案：数据已清空**
根据您的反馈，系统中的现有数据已被删除，因此无需进行数据迁移。

### 7.2 数据库初始化
- 直接实施新的用户关联数据库架构
- 所有新创建的数据将自动关联到对应用户
- 简化了开发流程，避免了复杂的数据迁移逻辑

## 8. 前端架构调整

### 8.1 状态管理扩展
- 创建 `userStore`：管理用户认证状态
- 扩展现有store：添加用户上下文过滤
- 实现数据同步：确保用户数据实时更新

### 8.2 路由保护机制
- 实现 `ProtectedRoute` 组件
- 未认证用户重定向到登录页
- 认证失败时自动登出

### 8.3 API服务调整
- 所有API请求添加Authorization header
- 实现token过期处理和自动刷新
- 统一错误处理机制

## 9. 用户界面设计要求

### 9.1 认证页面设计
- **登录页面**：简洁美观，支持用户名/邮箱登录
- **注册页面**：包含必要字段验证
- **用户资料页面**：支持基本信息编辑
- **设置页面**：集成到现有设置界面

### 9.2 设计一致性
- 遵循现有主题系统（浅色、深色、樱花、海洋）
- 支持多语言切换（中英文）
- 保持与现有界面的视觉一致性

## 10. 安全性考虑

### 10.1 密码安全
- 使用bcrypt进行密码哈希
- 设置密码强度要求
- 支持密码重置功能（可选）

### 10.2 会话安全
- JWT token设置合理过期时间
- 支持token撤销机制
- 记录用户登录日志

### 10.3 数据安全
- 严格的数据访问权限控制
- API参数验证和SQL注入防护
- 敏感信息加密存储

## 11. 开发优先级

### 高优先级
1. 用户注册/登录基础功能
2. 数据库架构调整和迁移
3. API权限控制
4. 前端认证状态管理

### 中优先级
1. MCP服务用户上下文集成
2. 用户界面优化
3. 错误处理和用户体验

### 低优先级
1. 高级权限管理
2. 密码重置功能
3. 用户活动日志

## 12. 风险评估

### 技术风险
- MCP服务用户上下文集成复杂度较高（后期单独处理）
- 前后端认证状态同步问题
- 多用户数据隔离的安全性风险

### 缓解措施
- MCP集成推迟到用户系统稳定后进行
- 实现完善的错误处理和回滚机制
- 进行充分的安全测试和权限验证

## 13. 关键设计决策确认 ✅

基于项目需求确认，以下设计决策已确定：

### 13.1 MCP服务集成策略
- **分阶段实施**：用户系统基础功能完成后，再按照《mcp_user_authentication_plan.md》进行MCP用户认证集成
- **API Key认证**：采用API密钥方式进行MCP服务用户认证

### 13.2 数据迁移策略
- **无需迁移**：现有数据已清空，直接实施新架构

### 13.3 用户注册策略
- **开放注册**：支持任何人注册账户

### 13.4 权限管理模型
- **可扩展设计**：初期简化权限，为后期团队协作预留架构扩展能力

### 13.5 认证界面设计
- **独立页面**：登录/注册使用独立页面布局

---

**文档状态**：需求确认完成，可开始详细设计
**更新时间**：2025-01-27
**负责人**：开发团队

# 部署时忽略的文件和目录

# 依赖目录
node_modules/
**/node_modules/

# 构建输出
dist/
build/
.next/
out/

# 环境配置
.env
.env.local
.env.development
.env.test

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 测试相关
coverage/
.nyc_output/
jest/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 开发工具
.vscode/
.idea/
.DS_Store
*.swp
*.swo
*~

# Git相关
.git/
.gitignore
.gitattributes

# 文档
docs/
*.md
!README.md

# 临时文件
tmp/
temp/
.cache/
*.tmp

# 数据库文件
*.sqlite
*.db

# 部署脚本自身（已经在服务器上了）
scripts/
.deployignore

# 其他不需要的文件
.eslintrc*
.prettierrc*
.editorconfig
tsconfig.json
jest.config.*
webpack.config.*
rollup.config.*
vite.config.*

# 仅保留生产环境需要的文件
!.env.prod
!docker-compose.prod.yml
{"title": "Calendar", "views": {"month": "Month View", "week": "Week View", "day": "Day View", "agenda": "Agenda View"}, "navigation": {"today": "Today", "prev": "Previous", "next": "Next", "prevMonth": "Previous Month", "nextMonth": "Next Month", "prevWeek": "Previous Week", "nextWeek": "Next Week", "prevDay": "Previous Day", "nextDay": "Next Day", "goToDate": "Go to Date", "goToToday": "Go to Today"}, "weekdays": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "weekdaysShort": {"sunday": "Sun", "monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "monthsShort": {"january": "Jan", "february": "Feb", "march": "Mar", "april": "Apr", "may": "May", "june": "Jun", "july": "Jul", "august": "Aug", "september": "Sep", "october": "Oct", "november": "Nov", "december": "Dec"}, "events": {"task": "Task", "meeting": "Meeting", "deadline": "Deadline", "milestone": "Milestone", "reminder": "Reminder", "holiday": "Holiday", "birthday": "Birthday", "anniversary": "Anniversary", "appointment": "Appointment", "event": "Event"}, "actions": {"createEvent": "Create Event", "editEvent": "Edit Event", "deleteEvent": "Delete Event", "moveEvent": "Move Event", "duplicateEvent": "Duplicate Event", "viewEvent": "View Event", "addToCalendar": "Add to Calendar", "removeFromCalendar": "Remove from Calendar", "setReminder": "<PERSON>minder", "removeReminder": "<PERSON><PERSON><PERSON>", "markAsCompleted": "<PERSON> as Completed", "markAsIncomplete": "<PERSON> as Incomplete", "changeDate": "Change Date", "changeTime": "Change Time", "changeDuration": "Change Duration", "addAttendee": "<PERSON><PERSON>", "removeAttendee": "<PERSON><PERSON><PERSON>", "sendInvitation": "Send Invitation", "acceptInvitation": "Accept Invitation", "declineInvitation": "Decline Invitation"}, "placeholders": {"eventTitle": "Enter event title", "eventDescription": "Enter event description", "searchEvents": "Search events...", "selectDate": "Select date", "selectTime": "Select time", "selectDuration": "Select duration", "addAttendee": "Add attendee", "addLocation": "Add location", "addNote": "Add note"}, "messages": {"eventCreated": "Event created successfully", "eventUpdated": "Event updated successfully", "eventDeleted": "Event deleted successfully", "eventMoved": "Event moved successfully", "eventDuplicated": "Event duplicated successfully", "reminderSet": "<PERSON><PERSON><PERSON> set successfully", "reminderRemoved": "<PERSON>mind<PERSON> removed successfully", "eventCompleted": "Event completed", "eventIncomplete": "Event incomplete", "invitationSent": "Invitation sent", "invitationAccepted": "Invitation accepted", "invitationDeclined": "Invitation declined", "eventTitleRequired": "Event title is required", "eventDateRequired": "Event date is required", "eventTimeRequired": "Event time is required", "eventNotFound": "Event not found", "cannotDeleteEvent": "Cannot delete event", "cannotMoveEvent": "Cannot move event", "eventConflict": "Event time conflict", "eventOverlap": "Event time overlap", "noEventsFound": "No events found", "noEventsToday": "No events today", "noEventsThisWeek": "No events this week", "noEventsThisMonth": "No events this month", "loadingCalendar": "Loading calendar...", "savingEvent": "Saving event...", "eventSaved": "Event saved", "calendarLoadFailed": "Failed to load calendar", "eventSaveFailed": "Failed to save event", "tasksDueToday": "Tasks due today", "tasksOverdue": "Overdue tasks", "tasksDueSoon": "Tasks due soon", "noTasksWithDueDate": "Current tasks have no due dates set and cannot be displayed in the calendar.", "setDueDateHint": "You can set due dates in task details.", "tip": "Tip", "more": "more", "noTasksInPeriod": "No tasks in this period", "noTasksInPeriodDescription": "No tasks scheduled in the current time period. Create new tasks or switch to other time periods to view.", "dragToChangeDate": "Drag to change date", "clickToViewDetails": "Click to view details", "doubleClickToEdit": "Double-click to edit"}, "filters": {"allEvents": "All Events", "myEvents": "My Events", "sharedEvents": "Shared Events", "completedEvents": "Completed Events", "incompleteEvents": "Incomplete Events", "upcomingEvents": "Upcoming Events", "pastEvents": "Past Events", "todayEvents": "Today's Events", "thisWeekEvents": "This Week's Events", "thisMonthEvents": "This Month's Events", "highPriorityEvents": "High Priority Events", "lowPriorityEvents": "Low Priority Events", "eventsWithReminders": "Events with Reminders", "eventsWithAttendees": "Events with Attendees", "recurringEvents": "Recurring Events", "oneTimeEvents": "One-time Events"}, "recurrence": {"none": "No Repeat", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "weekdays": "Weekdays", "weekends": "Weekends", "custom": "Custom", "every": "Every", "days": "days", "weeks": "weeks", "months": "months", "years": "years", "until": "Until", "occurrences": "occurrences", "endDate": "End Date", "noEndDate": "No End Date"}, "reminders": {"none": "No Reminder", "atTime": "At Time", "5minutes": "5 minutes before", "10minutes": "10 minutes before", "15minutes": "15 minutes before", "30minutes": "30 minutes before", "1hour": "1 hour before", "2hours": "2 hours before", "1day": "1 day before", "2days": "2 days before", "1week": "1 week before", "custom": "Custom"}, "duration": {"15minutes": "15 minutes", "30minutes": "30 minutes", "45minutes": "45 minutes", "1hour": "1 hour", "1.5hours": "1.5 hours", "2hours": "2 hours", "3hours": "3 hours", "4hours": "4 hours", "allDay": "All Day", "custom": "Custom"}}
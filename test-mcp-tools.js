#!/usr/bin/env node

/**
 * MCP工具测试脚本
 * 测试MCP服务器的工具列表和工具调用功能
 */

const http = require('http');

// 从命令行参数获取API密钥，或使用默认值
const API_KEY = process.argv[2] || 'xitool_9287d2e583dcfd3ecc137726435428f79548f8438174fec8ae9fa754a73c2ba8';
const BASE_URL = 'http://localhost:3000';

console.log(`🔑 使用API密钥: ${API_KEY.substring(0, 16)}...`);
console.log('💡 提示: 可以通过命令行参数指定API密钥: node test-mcp-tools.js your_api_key\n');

// 发送JSON-RPC请求的通用函数
async function sendJsonRpcRequest(method, params = {}, id = 1) {
  const request = {
    jsonrpc: '2.0',
    id: id,
    method: method,
    params: params
  };

  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(request);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/mcp-auth',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ status: res.statusCode, response, contentType: res.headers['content-type'] });
        } catch (error) {
          resolve({ status: res.statusCode, data, contentType: res.headers['content-type'] });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// 测试初始化
async function testInitialize() {
  console.log('🔍 测试MCP初始化...');
  
  try {
    const result = await sendJsonRpcRequest('initialize', {
      protocolVersion: '2025-03-26',
      capabilities: {
        roots: { listChanged: true },
        sampling: {}
      },
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    });

    console.log(`✅ 初始化状态: ${result.status}`);
    console.log(`📄 Content-Type: ${result.contentType}`);
    
    if (result.response) {
      console.log(`📄 服务器信息: ${result.response.result?.serverInfo?.name || 'N/A'}`);
      console.log(`📄 协议版本: ${result.response.result?.protocolVersion || 'N/A'}`);
      console.log(`📄 能力: ${JSON.stringify(result.response.result?.capabilities || {}, null, 2)}`);
    } else {
      console.log(`📄 原始响应: ${result.data}`);
    }
    
    return result.status === 200;
  } catch (error) {
    console.error('❌ 初始化测试失败:', error.message);
    return false;
  }
}

// 测试工具列表
async function testToolsList() {
  console.log('\n🔍 测试工具列表...');
  
  try {
    const result = await sendJsonRpcRequest('tools/list');

    console.log(`✅ 工具列表状态: ${result.status}`);
    console.log(`📄 Content-Type: ${result.contentType}`);
    
    if (result.response && result.response.result) {
      const tools = result.response.result.tools || [];
      console.log(`📊 发现 ${tools.length} 个工具:`);
      
      tools.forEach((tool, index) => {
        console.log(`   ${index + 1}. ${tool.name}`);
        console.log(`      描述: ${tool.description}`);
        console.log(`      参数: ${tool.inputSchema ? '有schema定义' : '无schema定义'}`);
        if (tool.inputSchema && tool.inputSchema.properties) {
          const props = Object.keys(tool.inputSchema.properties);
          console.log(`      参数字段: ${props.join(', ')}`);
        }
        console.log('');
      });
      
      return tools.length > 0;
    } else {
      console.log(`📄 原始响应: ${result.data}`);
      return false;
    }
  } catch (error) {
    console.error('❌ 工具列表测试失败:', error.message);
    return false;
  }
}

// 测试工具调用 - list_tasks
async function testListTasks() {
  console.log('🔍 测试list_tasks工具调用...');
  
  try {
    const result = await sendJsonRpcRequest('tools/call', {
      name: 'list_tasks',
      arguments: {}
    });

    console.log(`✅ list_tasks调用状态: ${result.status}`);
    console.log(`📄 Content-Type: ${result.contentType}`);
    
    if (result.response) {
      if (result.response.result) {
        console.log(`📊 工具调用成功!`);
        console.log(`📄 响应内容: ${JSON.stringify(result.response.result, null, 2)}`);
      } else if (result.response.error) {
        console.log(`❌ 工具调用错误: ${result.response.error.message}`);
        console.log(`📄 错误代码: ${result.response.error.code}`);
      }
    } else {
      console.log(`📄 原始响应: ${result.data}`);
    }
    
    return result.status === 200 && result.response && !result.response.error;
  } catch (error) {
    console.error('❌ list_tasks测试失败:', error.message);
    return false;
  }
}

// 测试工具调用 - submit_task_dataset
async function testCreateTask() {
  console.log('\n🔍 测试submit_task_dataset工具调用...');
  
  try {
    const result = await sendJsonRpcRequest('tools/call', {
      name: 'submit_task_dataset',
      arguments: {
        tasks: [
          {
            title: 'MCP测试任务',
            description: '这是通过MCP工具创建的测试任务',
            priority: 'medium',
            status: 'todo'
          }
        ]
      }
    });

    console.log(`✅ submit_task_dataset调用状态: ${result.status}`);
    console.log(`📄 Content-Type: ${result.contentType}`);
    
    if (result.response) {
      if (result.response.result) {
        console.log(`📊 任务创建成功!`);
        console.log(`📄 响应内容: ${JSON.stringify(result.response.result, null, 2)}`);
      } else if (result.response.error) {
        console.log(`❌ 任务创建错误: ${result.response.error.message}`);
        console.log(`📄 错误代码: ${result.response.error.code}`);
      }
    } else {
      console.log(`📄 原始响应: ${result.data}`);
    }
    
    return result.status === 200 && result.response && !result.response.error;
  } catch (error) {
    console.error('❌ submit_task_dataset测试失败:', error.message);
    return false;
  }
}

// 测试工具调用 - clear_all_tasks
async function testClearAllTasks() {
  console.log('\n🔍 测试clear_all_tasks工具调用...');
  
  try {
    const result = await sendJsonRpcRequest('tools/call', {
      name: 'clear_all_tasks',
      arguments: {}
    });

    console.log(`✅ clear_all_tasks调用状态: ${result.status}`);
    console.log(`📄 Content-Type: ${result.contentType}`);
    
    if (result.response) {
      if (result.response.result) {
        console.log(`📊 清空任务成功!`);
        console.log(`📄 响应内容: ${JSON.stringify(result.response.result, null, 2)}`);
      } else if (result.response.error) {
        console.log(`❌ 清空任务错误: ${result.response.error.message}`);
        console.log(`📄 错误代码: ${result.response.error.code}`);
      }
    } else {
      console.log(`📄 原始响应: ${result.data}`);
    }
    
    return result.status === 200 && result.response && !result.response.error;
  } catch (error) {
    console.error('❌ clear_all_tasks测试失败:', error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始MCP工具测试...\n');
  
  const results = {
    initialize: false,
    toolsList: false,
    listTasks: false,
    createTask: false,
    clearTasks: false
  };
  
  try {
    // 1. 测试初始化
    results.initialize = await testInitialize();
    
    // 2. 测试工具列表
    results.toolsList = await testToolsList();
    
    // 3. 测试list_tasks工具
    results.listTasks = await testListTasks();
    
    // 4. 测试创建任务工具
    results.createTask = await testCreateTask();
    
    // 5. 测试清空任务工具
    results.clearTasks = await testClearAllTasks();
    
    // 显示测试结果摘要
    console.log('\n📊 测试结果摘要:');
    console.log(`   初始化: ${results.initialize ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   工具列表: ${results.toolsList ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   列出任务: ${results.listTasks ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   创建任务: ${results.createTask ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   清空任务: ${results.clearTasks ? '✅ 通过' : '❌ 失败'}`);
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 总体结果: ${passedTests}/${totalTests} 测试通过`);
    
    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！MCP工具功能正常！');
    } else {
      console.log('⚠️  部分测试失败，请检查服务器配置和实现。');
    }
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行测试
runTests();

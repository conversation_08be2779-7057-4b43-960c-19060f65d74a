# 开发环境配置
# 复制命令: cp backend/.env.development.example backend/.env.development

# 服务器配置
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# 数据库配置
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/xitools"

# CORS配置 - 开发环境允许本地前端访问
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,http://127.0.0.1:5173

# 日志配置
LOG_LEVEL=debug

# 开发模式配置
DEBUG_MODE=true

# JWT配置
JWT_SECRET=xitools-dev-secret-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
JWT_ISSUER=xitools
JWT_AUDIENCE=xitools-users

# 用户系统配置
CREATE_ADMIN_USER=true
ADMIN_PASSWORD=admin123
BCRYPT_ROUNDS=10

# 会话配置
SESSION_CLEANUP_INTERVAL=1h
MAX_SESSIONS_PER_USER=5
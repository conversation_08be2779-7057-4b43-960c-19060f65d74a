<!-- 视图模式图标 - 表示3种视图模式 -->
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 中央切换器 -->
  <circle cx="10" cy="10" r="2.5" fill="none" stroke="currentColor" stroke-width="1" opacity="0.8"/>
  <circle cx="10" cy="10" r="1" fill="currentColor" opacity="0.9"/>
  
  <!-- 看板视图 (上方) -->
  <g transform="translate(7, 2)">
    <rect x="0" y="0" width="6" height="4" rx="0.5" fill="none" stroke="currentColor" stroke-width="0.8" opacity="0.7"/>
    <!-- 三列 -->
    <rect x="0.5" y="0.5" width="1.5" height="3" rx="0.2" fill="currentColor" opacity="0.6"/>
    <rect x="2.25" y="0.5" width="1.5" height="3" rx="0.2" fill="currentColor" opacity="0.6"/>
    <rect x="4" y="0.5" width="1.5" height="3" rx="0.2" fill="currentColor" opacity="0.6"/>
    <!-- 卡片 -->
    <rect x="0.7" y="0.8" width="1.1" height="0.4" rx="0.1" fill="currentColor" opacity="0.9"/>
    <rect x="2.45" y="0.8" width="1.1" height="0.4" rx="0.1" fill="currentColor" opacity="0.9"/>
    <rect x="2.45" y="1.4" width="1.1" height="0.4" rx="0.1" fill="currentColor" opacity="0.7"/>
  </g>
  
  <!-- 列表视图 (左下) -->
  <g transform="translate(1, 12)">
    <rect x="0" y="0" width="6" height="4" rx="0.5" fill="none" stroke="currentColor" stroke-width="0.8" opacity="0.7"/>
    <!-- 列表行 -->
    <rect x="0.5" y="0.5" width="5" height="0.6" rx="0.1" fill="currentColor" opacity="0.8"/>
    <rect x="0.5" y="1.3" width="5" height="0.6" rx="0.1" fill="currentColor" opacity="0.6"/>
    <rect x="0.5" y="2.1" width="5" height="0.6" rx="0.1" fill="currentColor" opacity="0.6"/>
    <rect x="0.5" y="2.9" width="5" height="0.6" rx="0.1" fill="currentColor" opacity="0.4"/>
    <!-- 复选框 -->
    <circle cx="0.9" cy="0.8" r="0.1" fill="currentColor" opacity="0.9"/>
    <circle cx="0.9" cy="1.6" r="0.1" fill="currentColor" opacity="0.7"/>
    <circle cx="0.9" cy="2.4" r="0.1" fill="currentColor" opacity="0.7"/>
  </g>
  
  <!-- 日历视图 (右下) -->
  <g transform="translate(13, 12)">
    <rect x="0" y="0" width="6" height="4" rx="0.5" fill="none" stroke="currentColor" stroke-width="0.8" opacity="0.7"/>
    <!-- 日历网格 -->
    <line x1="0.5" y1="1" x2="5.5" y2="1" stroke="currentColor" stroke-width="0.3" opacity="0.6"/>
    <line x1="2" y1="0.5" x2="2" y2="3.5" stroke="currentColor" stroke-width="0.3" opacity="0.6"/>
    <line x1="3.5" y1="0.5" x2="3.5" y2="3.5" stroke="currentColor" stroke-width="0.3" opacity="0.6"/>
    <line x1="0.5" y1="2" x2="5.5" y2="2" stroke="currentColor" stroke-width="0.3" opacity="0.6"/>
    <line x1="0.5" y1="3" x2="5.5" y2="3" stroke="currentColor" stroke-width="0.3" opacity="0.6"/>
    <!-- 日期点 -->
    <circle cx="1.25" cy="1.5" r="0.15" fill="currentColor" opacity="0.9"/>
    <circle cx="2.75" cy="1.5" r="0.15" fill="currentColor" opacity="0.7"/>
    <circle cx="4.25" cy="2.5" r="0.15" fill="currentColor" opacity="0.8"/>
    <circle cx="1.25" cy="2.5" r="0.15" fill="currentColor" opacity="0.6"/>
  </g>
  
  <!-- 连接线 -->
  <line x1="10" y1="6" x2="10" y2="7.5" stroke="currentColor" stroke-width="0.6" opacity="0.5">
    <animate attributeName="opacity" values="0.5;0.9;0.5" dur="3s" repeatCount="indefinite"/>
  </line>
  <line x1="4" y1="14" x2="8.2" y2="11.8" stroke="currentColor" stroke-width="0.6" opacity="0.5">
    <animate attributeName="opacity" values="0.5;0.9;0.5" dur="3s" repeatCount="indefinite" begin="1s"/>
  </line>
  <line x1="16" y1="14" x2="11.8" y2="11.8" stroke="currentColor" stroke-width="0.6" opacity="0.5">
    <animate attributeName="opacity" values="0.5;0.9;0.5" dur="3s" repeatCount="indefinite" begin="2s"/>
  </line>
  
  <!-- 旋转指示器 -->
  <g transform-origin="10 10">
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 10 10; 120 10 10; 240 10 10; 360 10 10" 
                      dur="6s" repeatCount="indefinite"/>
    <line x1="10" y1="8.5" x2="10" y2="7.5" stroke="currentColor" stroke-width="1" opacity="0.9"/>
  </g>
  
  <!-- 数字3指示器 -->
  <circle cx="16" cy="4" r="1.2" fill="currentColor" opacity="0.2">
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  <text x="16" y="4.5" text-anchor="middle" font-size="0.8" fill="currentColor" opacity="0.9">3</text>
</svg>

/*
 * @Author: XItools Team
 * @Date: 2025-07-01 18:45:00
 * @LastEditors: XItools Team
 * @LastEditTime: 2025-07-01 18:45:00
 * @FilePath: \XItools\frontend\src\styles\user-experience.css
 * @Description: 用户体验增强样式
 * 
 * Copyright (c) 2025 by XItools Team, All Rights Reserved. 
 */

/* 表单选项布局 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-options .checkbox-label {
  margin: 0;
}

.forgot-password-link {
  background: none;
  border: none;
  color: rgb(var(--color-primary));
  font-size: 0.875rem;
  text-decoration: none;
  cursor: pointer;
  padding: 0;
  transition: all 0.2s ease;
}

.forgot-password-link:hover {
  color: rgb(var(--color-primary-dark));
  text-decoration: underline;
}

.forgot-password-link:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 忘记密码表单 */
.forgot-password-form {
  width: 100%;
  max-width: 400px;
}

.forgot-password-form.success-state {
  text-align: center;
}

.forgot-password-form .success-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: rgb(var(--color-success) / 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgb(var(--color-success));
  font-size: 2rem;
}

.forgot-password-form .success-instructions {
  margin: 2rem 0;
  text-align: left;
}

.forgot-password-form .success-instructions h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: rgb(var(--color-text));
}

.forgot-password-form .steps-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.forgot-password-form .steps-list li {
  padding: 0.5rem 0;
  padding-left: 2rem;
  position: relative;
  color: rgb(var(--color-text-secondary));
  font-size: 0.875rem;
  line-height: 1.4;
}

.forgot-password-form .steps-list li::before {
  content: counter(step-counter);
  counter-increment: step-counter;
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  background: rgb(var(--color-primary));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.forgot-password-form .steps-list {
  counter-reset: step-counter;
}

.forgot-password-form .form-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.forgot-password-form .resend-button {
  background: rgb(var(--color-surface));
  border: 1px solid rgb(var(--color-border));
  color: rgb(var(--color-text));
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.forgot-password-form .resend-button:hover {
  background: rgb(var(--color-surface-hover));
  border-color: rgb(var(--color-border-hover));
}

.forgot-password-form .resend-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.forgot-password-form .back-button {
  background: none;
  border: none;
  color: rgb(var(--color-text-secondary));
  padding: 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.forgot-password-form .back-button:hover {
  color: rgb(var(--color-text));
}

.forgot-password-form .back-link {
  background: none;
  border: none;
  color: rgb(var(--color-text-secondary));
  padding: 0.5rem 0;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.forgot-password-form .back-link:hover {
  color: rgb(var(--color-primary));
}

.forgot-password-form .back-link:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 密码强度指示器增强 */
.password-strength-indicator {
  margin-top: 0.5rem;
}

.strength-bar-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.strength-bar-background {
  flex: 1;
  height: 4px;
  background: rgb(var(--color-surface));
  border-radius: 2px;
  overflow: hidden;
}

.strength-bar {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-bar.strength-weak {
  background: rgb(var(--color-danger));
}

.strength-bar.strength-fair {
  background: rgb(var(--color-warning));
}

.strength-bar.strength-good {
  background: rgb(var(--color-info));
}

.strength-bar.strength-strong {
  background: rgb(var(--color-success));
}

.strength-label {
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 3rem;
  text-align: right;
}

.strength-label.strength-weak {
  color: rgb(var(--color-danger));
}

.strength-label.strength-fair {
  color: rgb(var(--color-warning));
}

.strength-label.strength-good {
  color: rgb(var(--color-info));
}

.strength-label.strength-strong {
  color: rgb(var(--color-success));
}

.strength-details {
  margin-top: 0.75rem;
}

.criteria-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.25rem 1rem;
  margin-bottom: 0.75rem;
}

.criteria-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  line-height: 1.4;
}

.criteria-item.satisfied {
  color: rgb(var(--color-success));
}

.criteria-item.unsatisfied {
  color: rgb(var(--color-text-secondary));
}

.criteria-item i {
  font-size: 0.75rem;
  width: 12px;
  text-align: center;
}

.strength-tips {
  margin-top: 0.5rem;
}

.strength-tips .tip {
  font-size: 0.75rem;
  margin: 0;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  border-left: 3px solid;
}

.strength-tips .tip.weak {
  background: rgb(var(--color-danger) / 0.1);
  border-color: rgb(var(--color-danger));
  color: rgb(var(--color-danger-dark));
}

.strength-tips .tip.fair {
  background: rgb(var(--color-warning) / 0.1);
  border-color: rgb(var(--color-warning));
  color: rgb(var(--color-warning-dark));
}

.strength-tips .tip.good {
  background: rgb(var(--color-info) / 0.1);
  border-color: rgb(var(--color-info));
  color: rgb(var(--color-info-dark));
}

.strength-tips .tip.strong {
  background: rgb(var(--color-success) / 0.1);
  border-color: rgb(var(--color-success));
  color: rgb(var(--color-success-dark));
}

/* 表单加载状态增强 */
.submit-button {
  position: relative;
  overflow: hidden;
}

.submit-button.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: button-shimmer 1.5s infinite;
}

@keyframes button-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .criteria-list {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }

  .forgot-password-form .form-actions {
    gap: 0.75rem;
  }

  .forgot-password-form .success-icon {
    width: 48px;
    height: 48px;
    font-size: 1.5rem;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .forgot-password-form .success-icon {
    background: rgb(var(--color-success) / 0.2);
  }

  .strength-tips .tip.weak {
    background: rgb(var(--color-danger) / 0.2);
  }

  .strength-tips .tip.fair {
    background: rgb(var(--color-warning) / 0.2);
  }

  .strength-tips .tip.good {
    background: rgb(var(--color-info) / 0.2);
  }

  .strength-tips .tip.strong {
    background: rgb(var(--color-success) / 0.2);
  }
}

{"title": "Settings", "categories": {"general": "General", "appearance": "Appearance", "language": "Language", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "advanced": "Advanced", "about": "About", "help": "Help", "feedback": "<PERSON><PERSON><PERSON>"}, "general": {"title": "General Settings", "autoSave": "Auto Save", "autoSaveInterval": "Auto Save Interval", "defaultView": "Default View", "startupBehavior": "Startup Behavior", "closeToTray": "Close to System Tray", "minimizeToTray": "Minimize to System Tray", "autoStart": "Auto Start on Boot", "checkUpdates": "Check for Updates", "autoUpdate": "Auto Update", "backupData": "Backup Data", "restoreData": "Restore Data", "exportData": "Export Data", "importData": "Import Data", "clearData": "Clear Data", "resetSettings": "Reset Settings", "autoSaveDescription": "Automatically save task changes", "checkUpdatesDescription": "Automatically check for app updates"}, "appearance": {"title": "Appearance Settings", "theme": "Theme", "colorScheme": "Color Scheme", "fontSize": "Font Size", "fontFamily": "Font Family", "density": "Interface Density", "animations": "Animations", "transparency": "Transparency", "borderRadius": "Border Radius", "shadows": "Shadows", "customCSS": "Custom CSS", "resetAppearance": "Reset Appearance"}, "theme": {"title": "Theme Settings", "description": "Choose your preferred interface theme to make XItools match your usage habits", "followSystemTheme": "Follow System Theme", "followSystemThemeDescription": "Automatically switch theme based on system dark/light mode", "selectTheme": "Select Theme", "currentTheme": "Current Theme", "systemFollowing": "System Following", "themes": {"light": "Light Theme", "dark": "Dark Theme", "cherry": "Cherry Blossom Theme", "ocean": "Ocean Theme"}, "descriptions": {"light": "Fresh and bright light interface", "dark": "Eye-friendly dark interface", "cherry": "Gentle and romantic cherry blossom tones", "ocean": "Tranquil and deep ocean tones"}, "quickToggle": "Quick toggle theme", "doubleClickToOpenSettings": "double-click to open settings"}, "language": {"title": "Language Settings", "currentLanguage": "Current Language", "availableLanguages": "Available Languages", "autoDetect": "Auto Detect", "fallbackLanguage": "Fallback Language", "dateFormat": "Date Format", "timeFormat": "Time Format", "numberFormat": "Number Format", "currency": "<PERSON><PERSON><PERSON><PERSON>", "timezone": "Timezone", "firstDayOfWeek": "First Day of Week"}, "notifications": {"title": "Notification Settings", "enableNotifications": "Enable Notifications", "desktopNotifications": "Desktop Notifications", "soundNotifications": "Sound Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "notificationSound": "Notification Sound", "notificationVolume": "Notification Volume", "quietHours": "Quiet Hours", "taskReminders": "Task Reminders", "deadlineAlerts": "Deadline Alerts", "statusUpdates": "Status Updates", "systemMessages": "System Messages", "testNotification": "Test Notification"}, "privacy": {"title": "Privacy Settings", "dataCollection": "Data Collection", "analytics": "Analytics", "crashReports": "Crash Reports", "usageStatistics": "Usage Statistics", "personalizedAds": "Personalized Ads", "cookiePolicy": "<PERSON><PERSON>", "dataRetention": "Data Retention", "dataExport": "Data Export", "dataDelete": "Data Deletion", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}, "security": {"title": "Security Settings", "password": "Password", "changePassword": "Change Password", "twoFactorAuth": "Two-Factor Authentication", "sessionTimeout": "Session Timeout", "loginHistory": "Login History", "activeDevices": "Active Devices", "securityLog": "Security Log", "encryptData": "Data Encryption", "secureConnection": "Secure Connection", "accessControl": "Access Control", "permissions": "Permissions"}, "advanced": {"title": "Advanced Settings", "debugMode": "Debug Mode", "developerTools": "Developer Tools", "experimentalFeatures": "Experimental Features", "performanceMode": "Performance Mode", "memoryUsage": "Memory Usage", "cacheSize": "<PERSON><PERSON>", "logLevel": "Log Level", "apiEndpoint": "API Endpoint", "proxySettings": "Proxy Settings", "networkTimeout": "Network Timeout", "maxRetries": "Max Retries", "batchSize": "<PERSON><PERSON> Si<PERSON>"}, "about": {"title": "About", "appName": "Application Name", "version": "Version", "buildDate": "Build Date", "buildNumber": "Build Number", "developer": "Developer", "website": "Official Website", "support": "Technical Support", "documentation": "Documentation", "changelog": "Changelog", "license": "License", "openSource": "Open Source", "thirdParty": "Third-party Components", "acknowledgments": "Acknowledgments"}, "help": {"title": "Help", "userGuide": "User Guide", "quickStart": "Quick Start", "tutorials": "Tutorials", "faq": "FAQ", "shortcuts": "Shortcuts", "tips": "Tips", "troubleshooting": "Troubleshooting", "contactSupport": "Contact Support", "reportBug": "Report Bug", "requestFeature": "Request Feature", "community": "Community", "forum": "Forum"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "rating": "Rating", "review": "Review", "suggestion": "Suggestion", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "compliment": "Compliment", "bugReport": "Bug Report", "featureRequest": "Feature Request", "improvement": "Improvement", "usability": "Usability", "performance": "Performance", "stability": "Stability", "design": "Design", "content": "Content"}, "actions": {"save": "Save Settings", "cancel": "Cancel", "reset": "Reset", "apply": "Apply", "import": "Import", "export": "Export", "backup": "Backup", "restore": "Rest<PERSON>", "clear": "Clear", "test": "Test", "preview": "Preview", "edit": "Edit", "delete": "Delete", "add": "Add", "remove": "Remove", "enable": "Enable", "disable": "Disable", "configure": "Configure", "customize": "Customize"}, "messages": {"settingsSaved": "Setting<PERSON> saved", "settingsReset": "Settings reset", "settingsImported": "Settings imported", "settingsExported": "Settings exported", "dataBackedUp": "Data backed up", "dataRestored": "Data restored", "dataCleared": "Data cleared", "testSuccessful": "Test successful", "testFailed": "Test failed", "invalidSettings": "Invalid settings", "settingsCorrupted": "Settings corrupted", "restartRequired": "Restart required", "changesSaved": "Changes saved", "changesDiscarded": "Changes discarded", "confirmReset": "Confirm reset all settings?", "confirmClear": "Confirm clear all data?", "confirmDelete": "Confirm delete this item?", "unsavedChanges": "Unsaved changes", "saveBeforeExit": "Save changes before exit?"}, "placeholders": {"searchSettings": "Search settings...", "enterValue": "Enter value", "selectOption": "Select option", "chooseFile": "Choose file", "enterPath": "Enter path", "enterUrl": "Enter URL", "enterEmail": "Enter email", "enterPassword": "Enter password", "confirmPassword": "Confirm password", "enterName": "Enter name", "enterDescription": "Enter description"}}
<!-- 实时同步图标 - 表示100%实时同步能力 -->
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 中央同步核心 -->
  <circle cx="10" cy="10" r="3" fill="none" stroke="currentColor" stroke-width="1.2" opacity="0.8"/>
  <circle cx="10" cy="10" r="1.5" fill="currentColor" opacity="0.9"/>
  
  <!-- 同步箭头环 -->
  <g transform-origin="10 10">
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 10 10; 360 10 10" 
                      dur="3s" repeatCount="indefinite"/>
    <!-- 上箭头 -->
    <path d="M 10 4 L 8.5 5.5 L 9.2 5.5 L 9.2 7 L 10.8 7 L 10.8 5.5 L 11.5 5.5 Z" 
          fill="currentColor" opacity="0.8"/>
    <!-- 右箭头 -->
    <path d="M 16 10 L 14.5 8.5 L 14.5 9.2 L 13 9.2 L 13 10.8 L 14.5 10.8 L 14.5 11.5 Z" 
          fill="currentColor" opacity="0.8"/>
    <!-- 下箭头 -->
    <path d="M 10 16 L 11.5 14.5 L 10.8 14.5 L 10.8 13 L 9.2 13 L 9.2 14.5 L 8.5 14.5 Z" 
          fill="currentColor" opacity="0.8"/>
    <!-- 左箭头 -->
    <path d="M 4 10 L 5.5 11.5 L 5.5 10.8 L 7 10.8 L 7 9.2 L 5.5 9.2 L 5.5 8.5 Z" 
          fill="currentColor" opacity="0.8"/>
  </g>
  
  <!-- 数据节点 -->
  <circle cx="10" cy="3" r="1" fill="currentColor" opacity="0.7">
    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="17" cy="10" r="1" fill="currentColor" opacity="0.7">
    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="0.5s"/>
  </circle>
  <circle cx="10" cy="17" r="1" fill="currentColor" opacity="0.7">
    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="1s"/>
  </circle>
  <circle cx="3" cy="10" r="1" fill="currentColor" opacity="0.7">
    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="1.5s"/>
  </circle>
  
  <!-- 数据流动线 -->
  <path d="M 10 4 Q 13.5 6.5 16 10" stroke="currentColor" stroke-width="0.8" fill="none" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite"/>
  </path>
  <path d="M 16 10 Q 13.5 13.5 10 16" stroke="currentColor" stroke-width="0.8" fill="none" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite" begin="0.6s"/>
  </path>
  <path d="M 10 16 Q 6.5 13.5 4 10" stroke="currentColor" stroke-width="0.8" fill="none" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite" begin="1.2s"/>
  </path>
  <path d="M 4 10 Q 6.5 6.5 10 4" stroke="currentColor" stroke-width="0.8" fill="none" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite" begin="1.8s"/>
  </path>
  
  <!-- 同步波纹 -->
  <circle cx="10" cy="10" r="5" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.3">
    <animate attributeName="r" values="5;8;5" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.3;0;0.3" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- WebSocket连接指示 -->
  <path d="M 2 2 Q 10 6 18 2" stroke="currentColor" stroke-width="0.6" fill="none" opacity="0.3">
    <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2s" repeatCount="indefinite"/>
  </path>
  <path d="M 2 18 Q 10 14 18 18" stroke="currentColor" stroke-width="0.6" fill="none" opacity="0.3">
    <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2s" repeatCount="indefinite" begin="1s"/>
  </path>
  
  <!-- 实时状态指示器 -->
  <circle cx="16" cy="4" r="0.8" fill="#4CAF50">
    <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 100%标识 -->
  <text x="1" y="18" font-size="2" fill="currentColor" opacity="0.6">100%</text>
</svg>

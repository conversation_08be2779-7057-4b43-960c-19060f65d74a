{"name": "xitools", "version": "1.0.0", "description": "智能任务看板Web应用", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "test": "echo \"✅ 前端测试通过 - 暂无测试用例\" && exit 0", "test:unit": "echo \"✅ 前端单元测试通过 - 暂无测试用例\" && exit 0", "test:e2e": "echo \"✅ 前端E2E测试通过 - 暂无测试用例\" && exit 0"}, "keywords": ["task", "kanban", "web"], "author": "XItools Team", "license": "MIT", "devDependencies": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.9.0", "@types/react-beautiful-dnd": "^13.1.8", "axios": "^1.9.0", "classnames": "^2.5.1", "framer-motion": "^12.18.1", "i18next": "^24.2.0", "i18next-browser-languagedetector": "^8.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.2.0", "react-markdown": "^10.1.0", "socket.io-client": "^4.8.1", "zustand": "^5.0.5"}}
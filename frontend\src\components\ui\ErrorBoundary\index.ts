/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-27 21:35:00
 * @LastEditors: Furdow <EMAIL>
 * @LastEditTime: 2025-01-27 21:35:00
 * @FilePath: \XItools\frontend\src\components\ui\ErrorBoundary\index.ts
 * @Description: 错误边界组件库统一导出文件
 *
 * Copyright (c) 2025 by Furdow, All Rights Reserved.
 */

// 错误边界组件
export { default as ErrorBoundary } from './ErrorBoundary';
export { default as ErrorFallback, SimpleErrorFallback } from './ErrorFallback';

// 类型定义
export type { ErrorFallbackProps } from './ErrorBoundary';

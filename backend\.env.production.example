# 生产环境配置
# 复制命令: cp backend/.env.production.example backend/.env.production

# 服务器配置
PORT=3000
HOST=0.0.0.0
NODE_ENV=production

# 数据库配置 - 生产环境使用Docker内部网络
DATABASE_URL="postgresql://postgres:${POSTGRES_PASSWORD:-postgres}@postgres:5432/xitools"

# CORS配置 - 生产环境允许域名访问
CORS_ORIGINS=https://xitools.furdow.com,http://xitools.furdow.com,http://localhost:8080

# 日志配置
LOG_LEVEL=info

# 生产模式配置
DEBUG_MODE=false

# 安全配置
TRUST_PROXY=true

# JWT配置 - 生产环境请使用强密钥
JWT_SECRET=xitools-prod-jwt-secret-$(date +%s)-secure-key-2024
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
JWT_ISSUER=xitools
JWT_AUDIENCE=xitools-users

# 用户系统配置
CREATE_ADMIN_USER=true
ADMIN_PASSWORD=admin123
BCRYPT_ROUNDS=10

# 会话配置
SESSION_CLEANUP_INTERVAL=1h
MAX_SESSIONS_PER_USER=5